# AI Coding Report Images Download Script
# 画像URLのリスト
$imageUrls = @(
    "https://developer.qcloudimg.com/http-save/10625241/8d2ec50881ffc7fc5b0bb7a746869b3a.webp",
    "https://developer.qcloudimg.com/http-save/10625241/0c126d077b70676966d0c042b66cf61a.webp",
    "https://developer.qcloudimg.com/http-save/10625241/d7103a06504107c7d54ccfcddea16dcf.webp",
    "https://developer.qcloudimg.com/http-save/10625241/c59ffd426845763234792da617e6545d.webp",
    "https://developer.qcloudimg.com/http-save/10625241/4cfa0e7c2f34c2211cd1f3e7e92f7499.webp",
    "https://developer.qcloudimg.com/http-save/10625241/4191c8400b79b56e6fffc4cbe8a1e435.webp",
    "https://developer.qcloudimg.com/http-save/10625241/15ac56fc3d01e116b25bb6ef668f7226.webp",
    "https://developer.qcloudimg.com/http-save/10625241/5ede99a55ea02e5b9c72c883c77fa5d4.webp",
    "https://developer.qcloudimg.com/http-save/10625241/ba6b4282cf09f9b8491a3621055eba30.webp",
    "https://developer.qcloudimg.com/http-save/10625241/b33350edefba73230b88b0e0bec09082.webp",
    "https://developer.qcloudimg.com/http-save/10625241/90dedaa6b1c0db404eeb534dc01b8b38.webp",
    "https://developer.qcloudimg.com/http-save/10625241/76a3c73e87e398f3652afc17ae8002f0.webp",
    "https://developer.qcloudimg.com/http-save/10625241/dc87b01704b912891fd48724fbe978df.webp",
    "https://developer.qcloudimg.com/http-save/10625241/79f79ca09417ebcaf548951fd4c0d39d.webp",
    "https://developer.qcloudimg.com/http-save/10625241/320d174ff7a47e7879756b628f86c7de.webp",
    "https://developer.qcloudimg.com/http-save/10625241/6da4a64cd49d28cae563963f54c84672.webp",
    "https://developer.qcloudimg.com/http-save/10625241/7b2e891ddb1d142f6e61487c4f46c449.webp",
    "https://developer.qcloudimg.com/http-save/10625241/9a8439d2ff668e39d34ca175a3f3b725.webp",
    "https://developer.qcloudimg.com/http-save/10625241/6a66c80e00575819b5b3a11a892971da.webp",
    "https://developer.qcloudimg.com/http-save/10625241/cdb625e37b24ef3bf38421a47ae368de.webp",
    "https://developer.qcloudimg.com/http-save/10625241/f7e7b18b6f959ad315ffdc1ff05a4af0.webp",
    "https://developer.qcloudimg.com/http-save/10625241/804616ab40d6c93377f3ef62a22a5aac.webp",
    "https://developer.qcloudimg.com/http-save/10625241/b9d95391ad6627de4a570f71e37c4502.webp",
    "https://developer.qcloudimg.com/http-save/10625241/af50b5ae9f2d704b0c156e8e219338ba.webp",
    "https://developer.qcloudimg.com/http-save/10625241/96f9eb569e53e2788184eeca04ba764b.webp",
    "https://developer.qcloudimg.com/http-save/10625241/0677838ffccf07384054d086ea9b75d7.webp",
    "https://developer.qcloudimg.com/http-save/10625241/97fd660ba70a02b4fe230fd36e5f8ed7.webp",
    "https://developer.qcloudimg.com/http-save/10625241/96fb647098cada5a59c28ef7fac60b43.webp",
    "https://developer.qcloudimg.com/http-save/10625241/8eaffc860c1ca2675ce0154e7d0df536.webp",
    "https://developer.qcloudimg.com/http-save/10625241/ad4248839791ffbbccd11d88551c26b2.webp",
    "https://developer.qcloudimg.com/http-save/10625241/362e37c494c0cff83dd7f11028bd431b.webp",
    "https://developer.qcloudimg.com/http-save/10625241/a47c4461c3f2c8faf9f85bc63aa63d2b.webp",
    "https://developer.qcloudimg.com/http-save/10625241/e4c07486517a9ec813fa35d5775011e5.webp",
    "https://developer.qcloudimg.com/http-save/10625241/ef1d5ae5a9540c0ce196e105b2a2bab4.webp",
    "https://developer.qcloudimg.com/http-save/10625241/065d481f0e518d9c0ee992e9f7584ba3.webp",
    "https://developer.qcloudimg.com/http-save/10625241/459872911e6ac065329d22e2ae1a24eb.webp",
    "https://developer.qcloudimg.com/http-save/10625241/1cc8b62e896a369288063ee9383f8396.webp",
    "https://developer.qcloudimg.com/http-save/10625241/7771c7f3312cd04e985a1fdf2228f4a0.webp",
    "https://developer.qcloudimg.com/http-save/10625241/1421b6b02fb2781b48c8b45510b41f41.webp",
    "https://developer.qcloudimg.com/http-save/10625241/027b59bb990233243cbdba13260e07b5.webp",
    "https://developer.qcloudimg.com/http-save/10625241/49b8c80cd8a4c5f9adce5b183fe61a17.webp",
    "https://developer.qcloudimg.com/http-save/10625241/79737defad7b82cb27f409f38faf4a4d.webp",
    "https://developer.qcloudimg.com/http-save/10625241/b65b094a39c1b7ba143cb1f4c77e511e.webp",
    "https://developer.qcloudimg.com/http-save/10625241/dd2bf1a0e8331ee9f4989e7171b7bb86.webp",
    "https://developer.qcloudimg.com/http-save/10625241/572e51b5214078455ff531f089e2967b.webp",
    "https://developer.qcloudimg.com/http-save/10625241/1d4cee0499a1c693e9f7e0dcda0cf54b.webp",
    "https://developer.qcloudimg.com/http-save/10625241/d150621ac4ad2658148abe643e01801b.webp",
    "https://developer.qcloudimg.com/http-save/10625241/6253e79e6f6558029474eee68b5d7c2b.webp",
    "https://developer.qcloudimg.com/http-save/10625241/ffd664bdba1b4ffffa1efe13f22e81bb.webp",
    "https://developer.qcloudimg.com/http-save/10625241/7cbb3a86f4e948034e851c4742a57b6b.webp",
    "https://developer.qcloudimg.com/http-save/10625241/8ecc8a0032c2f9a1fca8d3b2c3f48af2.webp",
    "https://developer.qcloudimg.com/http-save/10625241/d9d936f07dd45306e128ae701cde901b.webp",
    "https://developer.qcloudimg.com/http-save/10625241/1af25c189f5dc67efa04eb34295c186e.webp"
)

# 画像をダウンロードする関数
function Download-Images {
    $counter = 1
    foreach ($url in $imageUrls) {
        $fileName = "ai_coding_report_images\page_{0:D2}.webp" -f $counter
        Write-Host "ダウンロード中: $fileName"
        try {
            Invoke-WebRequest -Uri $url -OutFile $fileName -ErrorAction Stop
            Write-Host "成功: $fileName" -ForegroundColor Green
        }
        catch {
            Write-Host "エラー: $fileName - $($_.Exception.Message)" -ForegroundColor Red
        }
        $counter++
    }
}

# ダウンロード実行
Write-Host "AI Coding Report画像のダウンロードを開始します..."
Download-Images
Write-Host "ダウンロード完了!"
