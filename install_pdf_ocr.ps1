# PDF图片OCR安装脚本
Write-Host "开始安装PDF图片OCR相关库..." -ForegroundColor Green

# 检查Python
try {
    $pythonVersion = python --version
    Write-Host "Python版本: $pythonVersion" -ForegroundColor Green
}
catch {
    Write-Host "错误: 未找到Python，请先安装Python" -ForegroundColor Red
    exit 1
}

# 升级pip
Write-Host "升级pip..." -ForegroundColor Yellow
python -m pip install --upgrade pip

# 安装PDF处理库
Write-Host "安装PDF处理库..." -ForegroundColor Yellow
pip install PyPDF2
pip install PyMuPDF  # fitz
pip install pdfplumber

# 安装图像处理库
Write-Host "安装图像处理库..." -ForegroundColor Yellow
pip install Pillow
pip install opencv-python
pip install numpy

# 安装OCR库
Write-Host "安装OCR库..." -ForegroundColor Yellow
pip install easyocr
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu

# 安装pdf2image（需要poppler）
Write-Host "安装pdf2image..." -ForegroundColor Yellow
pip install pdf2image

# 检查是否需要安装poppler
Write-Host "检查poppler依赖..." -ForegroundColor Yellow

# 尝试安装poppler（Windows）
try {
    # 检查是否已安装
    $popplerPath = Get-Command pdftoppm -ErrorAction SilentlyContinue
    if ($popplerPath) {
        Write-Host "Poppler已安装: $($popplerPath.Source)" -ForegroundColor Green
    }
    else {
        Write-Host "尝试安装Poppler..." -ForegroundColor Yellow
        
        # 尝试使用chocolatey安装
        if (Get-Command choco -ErrorAction SilentlyContinue) {
            choco install poppler -y
        }
        # 尝试使用winget安装
        elseif (Get-Command winget -ErrorAction SilentlyContinue) {
            winget install --id=poppler.poppler
        }
        else {
            Write-Host "警告: 无法自动安装Poppler" -ForegroundColor Red
            Write-Host "请手动安装Poppler:" -ForegroundColor Yellow
            Write-Host "1. 访问: https://github.com/oschwartz10612/poppler-windows/releases" -ForegroundColor Blue
            Write-Host "2. 下载并解压到C:\poppler" -ForegroundColor Blue
            Write-Host "3. 将C:\poppler\bin添加到系统PATH" -ForegroundColor Blue
        }
    }
}
catch {
    Write-Host "Poppler安装检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 可选：安装Tesseract
Write-Host "是否安装Tesseract OCR? (y/n)" -ForegroundColor Cyan
$installTesseract = Read-Host

if ($installTesseract -eq 'y' -or $installTesseract -eq 'Y') {
    Write-Host "安装Tesseract..." -ForegroundColor Yellow
    pip install pytesseract
    
    try {
        winget install UB-Mannheim.TesseractOCR
        Write-Host "Tesseract安装完成" -ForegroundColor Green
    }
    catch {
        Write-Host "Tesseract自动安装失败，请手动安装" -ForegroundColor Red
        Write-Host "下载地址: https://github.com/UB-Mannheim/tesseract/wiki" -ForegroundColor Blue
    }
}

Write-Host "安装完成!" -ForegroundColor Green
Write-Host "现在可以使用以下命令处理PDF:" -ForegroundColor Cyan
Write-Host "python pdf_image_ocr.py your_file.pdf" -ForegroundColor White

# 创建使用示例
$exampleContent = @"
# PDF图片OCR使用示例

## 基本用法
```bash
# 处理PDF文件
python pdf_image_ocr.py document.pdf

# 或者运行脚本后输入文件路径
python pdf_image_ocr.py
```

## 支持的PDF类型
1. **文本PDF**: 直接提取文字，速度快
2. **扫描PDF**: 使用OCR提取文字
3. **图片PDF**: 转换为图片后OCR
4. **混合PDF**: 自动检测并选择最佳方法

## 输出文件
- 生成 `filename_extracted_text.md` 文件
- 包含所有页面的文字内容
- 按页面组织，便于阅读

## 注意事项
1. 首次运行OCR会下载模型文件（约100MB）
2. 处理大文件可能需要较长时间
3. 图片质量影响OCR准确率
4. 建议使用300DPI以上的PDF
"@

$exampleContent | Out-File -FilePath "PDF_OCR_使用说明.md" -Encoding UTF8
Write-Host "使用说明已保存到: PDF_OCR_使用说明.md" -ForegroundColor Cyan
