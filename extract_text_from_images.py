#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI Coding报告图片文字提取工具
使用EasyOCR提取WebP图片中的文字内容
"""

import os
import sys
from pathlib import Path

def check_dependencies():
    """检查必要的依赖库"""
    required_packages = ['easyocr', 'PIL', 'cv2']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'cv2':
                import cv2
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"缺少依赖库: {', '.join(missing_packages)}")
        print("请运行安装脚本: powershell -ExecutionPolicy Bypass -File install_ocr_requirements.ps1")
        return False
    
    return True

def extract_text_from_image(image_path, reader):
    """从单张图片提取文字"""
    try:
        print(f"正在处理: {os.path.basename(image_path)}")
        
        # 使用EasyOCR读取图片
        results = reader.readtext(str(image_path))
        
        # 提取文字，按位置排序
        text_blocks = []
        for (bbox, text, confidence) in results:
            if confidence > 0.3:  # 降低置信度阈值以获取更多文字
                # 获取文字块的y坐标用于排序
                y_coord = bbox[0][1]
                text_blocks.append((y_coord, text.strip()))
        
        # 按y坐标排序，模拟从上到下的阅读顺序
        text_blocks.sort(key=lambda x: x[0])
        
        # 组合文字
        extracted_text = '\n'.join([text for _, text in text_blocks])
        
        return extracted_text
        
    except Exception as e:
        print(f"处理图片时出错 {image_path}: {e}")
        return ""

def main():
    """主函数"""
    # 检查依赖
    if not check_dependencies():
        return
    
    # 导入库
    import easyocr
    
    # 设置路径
    image_folder = Path("ai_coding_report_images")
    output_file = Path("extracted_text_ocr.md")
    
    if not image_folder.exists():
        print(f"图片文件夹不存在: {image_folder}")
        return
    
    # 初始化EasyOCR（支持中英文）
    print("初始化OCR引擎...")
    try:
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)  # 使用CPU
    except Exception as e:
        print(f"初始化OCR引擎失败: {e}")
        return
    
    # 获取所有WebP文件
    image_files = list(image_folder.glob("page_*.webp"))
    image_files.sort(key=lambda x: int(x.stem.split('_')[1]))  # 按页码排序
    
    if not image_files:
        print("未找到WebP图片文件")
        return
    
    print(f"找到 {len(image_files)} 张图片")
    
    # 开始提取文字
    all_extracted_text = []
    
    for i, image_file in enumerate(image_files, 1):
        print(f"进度: {i}/{len(image_files)}")
        
        # 提取文字
        text = extract_text_from_image(image_file, reader)
        
        # 格式化输出
        page_content = f"""
## Page {i:02d} - {image_file.name}

{text if text.strip() else "未检测到文字内容"}

---
"""
        all_extracted_text.append(page_content)
        
        # 显示提取的文字长度
        char_count = len(text.strip())
        print(f"提取文字: {char_count} 字符")
    
    # 保存结果
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# AI Coding非共识报告 - OCR文字提取结果\n")
            f.write(f"\n提取时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"处理图片数量: {len(image_files)}\n")
            f.write(f"使用OCR引擎: EasyOCR\n\n")
            f.write("".join(all_extracted_text))
        
        print(f"\n✅ 文字提取完成!")
        print(f"结果已保存到: {output_file}")
        print(f"总共处理了 {len(image_files)} 张图片")
        
    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == "__main__":
    main()
