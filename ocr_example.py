# OCR图片文字提取示例
# 需要先安装相关库：pip install pytesseract Pillow easyocr paddleocr

import os
from PIL import Image
import pytesseract
import easyocr
import cv2
import numpy as np

class ImageOCR:
    def __init__(self):
        # 设置Tesseract路径（Windows）
        # pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
        
        # 初始化EasyOCR
        self.easy_reader = None
        
    def preprocess_image(self, image_path):
        """图像预处理，提高OCR准确率"""
        img = cv2.imread(image_path)
        
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 降噪
        denoised = cv2.medianBlur(gray, 3)
        
        # 二值化
        _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return binary
    
    def tesseract_ocr(self, image_path, lang='chi_sim+eng'):
        """使用Tesseract进行OCR"""
        try:
            # 预处理图像
            processed_img = self.preprocess_image(image_path)
            
            # OCR识别
            custom_config = r'--oem 3 --psm 6'
            text = pytesseract.image_to_string(processed_img, lang=lang, config=custom_config)
            
            return text.strip()
        except Exception as e:
            print(f"Tesseract OCR错误: {e}")
            return ""
    
    def easyocr_extract(self, image_path):
        """使用EasyOCR进行文字提取"""
        try:
            if self.easy_reader is None:
                # 初始化EasyOCR（支持中英文）
                self.easy_reader = easyocr.Reader(['ch_sim', 'en'])
            
            results = self.easy_reader.readtext(image_path)
            
            # 提取文字内容
            text_list = []
            for (bbox, text, confidence) in results:
                if confidence > 0.5:  # 置信度阈值
                    text_list.append(text)
            
            return '\n'.join(text_list)
        except Exception as e:
            print(f"EasyOCR错误: {e}")
            return ""
    
    def paddleocr_extract(self, image_path):
        """使用PaddleOCR进行文字提取"""
        try:
            from paddleocr import PaddleOCR
            
            # 初始化PaddleOCR
            ocr = PaddleOCR(use_angle_cls=True, lang='ch')
            
            result = ocr.ocr(image_path, cls=True)
            
            # 提取文字内容
            text_list = []
            for idx in range(len(result)):
                res = result[idx]
                for line in res:
                    text_list.append(line[1][0])
            
            return '\n'.join(text_list)
        except Exception as e:
            print(f"PaddleOCR错误: {e}")
            return ""

def batch_ocr_images(image_folder, output_file, ocr_method='easyocr'):
    """批量处理图片并提取文字"""
    ocr = ImageOCR()
    
    # 获取所有webp文件
    image_files = [f for f in os.listdir(image_folder) if f.endswith('.webp')]
    image_files.sort()  # 按文件名排序
    
    all_text = []
    
    for i, filename in enumerate(image_files, 1):
        image_path = os.path.join(image_folder, filename)
        print(f"处理第{i}张图片: {filename}")
        
        # 根据选择的方法进行OCR
        if ocr_method == 'tesseract':
            text = ocr.tesseract_ocr(image_path)
        elif ocr_method == 'easyocr':
            text = ocr.easyocr_extract(image_path)
        elif ocr_method == 'paddleocr':
            text = ocr.paddleocr_extract(image_path)
        else:
            print(f"未知的OCR方法: {ocr_method}")
            continue
        
        # 添加页面标识
        page_text = f"\n## Page {i:02d} ({filename})\n\n{text}\n\n---\n"
        all_text.append(page_text)
        
        print(f"提取文字长度: {len(text)} 字符")
    
    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# AI Coding非共识报告 - OCR文字提取结果\n\n")
        f.write("".join(all_text))
    
    print(f"OCR结果已保存到: {output_file}")

if __name__ == "__main__":
    # 使用示例
    image_folder = "ai_coding_report_images"
    output_file = "ocr_extracted_text.md"
    
    # 选择OCR方法：'tesseract', 'easyocr', 'paddleocr'
    ocr_method = 'easyocr'  # 推荐使用EasyOCR
    
    print(f"开始使用{ocr_method}进行批量OCR...")
    batch_ocr_images(image_folder, output_file, ocr_method)
