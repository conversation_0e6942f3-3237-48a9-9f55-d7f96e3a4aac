# AI Coding Report Images Download Script - Final Images
Write-Host "最後の画像をダウンロードします..."

# 最後の画像URLとファイル名のペア
$images = @(
    @{url="https://developer.qcloudimg.com/http-save/10625241/8ecc8a0032c2f9a1fca8d3b2c3f48af2.webp"; name="page_51.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/d9d936f07dd45306e128ae701cde901b.webp"; name="page_52.webp"}
)

foreach ($image in $images) {
    $filePath = "ai_coding_report_images\$($image.name)"
    Write-Host "ダウンロード中: $($image.name)"
    try {
        Invoke-WebRequest -Uri $image.url -OutFile $filePath -ErrorAction Stop
        Write-Host "成功: $($image.name)" -ForegroundColor Green
    }
    catch {
        Write-Host "エラー: $($image.name) - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "全ての画像のダウンロード完了!"
