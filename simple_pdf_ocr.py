#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版PDF图片OCR工具
专门处理扫描版PDF和图片PDF
"""

import os
import sys
from pathlib import Path

def simple_pdf_to_text(pdf_path, output_file=None):
    """简单的PDF转文字工具"""
    
    # 检查文件是否存在
    if not os.path.exists(pdf_path):
        print(f"文件不存在: {pdf_path}")
        return False
    
    # 设置输出文件名
    if output_file is None:
        pdf_name = Path(pdf_path).stem
        output_file = f"{pdf_name}_text.md"
    
    print(f"开始处理PDF: {pdf_path}")
    
    # 方法1: 尝试直接提取文字（适用于文本PDF）
    try:
        import fitz  # PyMuPDF
        
        doc = fitz.open(pdf_path)
        all_text = []
        has_text = False
        
        print("尝试直接提取文字...")
        for page_num in range(len(doc)):
            page = doc[page_num]
            text = page.get_text()
            
            if text.strip():
                has_text = True
                page_text = f"\n## 第 {page_num + 1} 页\n\n{text}\n\n---\n"
                all_text.append(page_text)
        
        doc.close()
        
        # 如果提取到了足够的文字，直接保存
        if has_text and len("".join(all_text)) > 500:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"# PDF文字提取结果 - {Path(pdf_path).name}\n\n")
                f.write("提取方式: 直接文字提取\n\n")
                f.write("".join(all_text))
            
            print(f"✅ 文字提取完成: {output_file}")
            return True
        
    except ImportError:
        print("PyMuPDF未安装，跳过直接文字提取")
    except Exception as e:
        print(f"直接文字提取失败: {e}")
    
    # 方法2: PDF转图片再OCR（适用于扫描PDF）
    try:
        from pdf2image import convert_from_path
        import easyocr
        
        print("PDF转图片并进行OCR...")
        
        # 初始化OCR
        print("初始化OCR引擎...")
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        
        # 转换PDF为图片
        print("转换PDF为图片...")
        images = convert_from_path(pdf_path, dpi=200)  # 降低DPI以提高速度
        
        all_text = []
        
        for i, image in enumerate(images):
            print(f"OCR处理第 {i+1}/{len(images)} 页...")
            
            # 保存临时图片
            temp_image = f"temp_page_{i+1}.png"
            image.save(temp_image)
            
            try:
                # OCR识别
                results = reader.readtext(temp_image)
                
                # 提取文字
                text_blocks = []
                for (bbox, text, confidence) in results:
                    if confidence > 0.3:
                        y_coord = bbox[0][1]
                        text_blocks.append((y_coord, text.strip()))
                
                # 按位置排序
                text_blocks.sort(key=lambda x: x[0])
                page_text = '\n'.join([text for _, text in text_blocks])
                
                # 添加到结果
                formatted_text = f"\n## 第 {i+1} 页\n\n{page_text if page_text.strip() else '未检测到文字'}\n\n---\n"
                all_text.append(formatted_text)
                
            finally:
                # 删除临时图片
                if os.path.exists(temp_image):
                    os.remove(temp_image)
        
        # 保存结果
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"# PDF文字提取结果 - {Path(pdf_path).name}\n\n")
            f.write("提取方式: PDF转图片OCR\n")
            f.write(f"总页数: {len(images)}\n\n")
            f.write("".join(all_text))
        
        print(f"✅ OCR处理完成: {output_file}")
        return True
        
    except ImportError as e:
        print(f"缺少依赖库: {e}")
        print("请运行: pip install pdf2image easyocr PyMuPDF")
        return False
    except Exception as e:
        print(f"OCR处理失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 简化版PDF图片OCR工具 ===\n")
    
    # 获取PDF文件路径
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    else:
        pdf_path = input("请输入PDF文件路径: ").strip().strip('"')
    
    # 处理PDF
    success = simple_pdf_to_text(pdf_path)
    
    if success:
        print("\n处理完成！")
    else:
        print("\n处理失败，请检查依赖库是否正确安装")
        print("安装命令: pip install PyMuPDF pdf2image easyocr")

if __name__ == "__main__":
    main()
