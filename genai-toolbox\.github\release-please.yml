# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

handleGHRelease: true
packageName: genai-toolbox
releaseType: simple
versionFile: "cmd/version.txt"
extraFiles: [
    "README.md",
    "docs/en/getting-started/colab_quickstart.ipynb",
    "docs/en/getting-started/introduction/_index.md",
    "docs/en/getting-started/local_quickstart.md",
    "docs/en/getting-started/local_quickstart_js.md",
    "docs/en/getting-started/local_quickstart_go.md",
    "docs/en/getting-started/mcp_quickstart/_index.md",
    "docs/en/samples/bigquery/local_quickstart.md",
    "docs/en/samples/bigquery/mcp_quickstart/_index.md",
    "docs/en/samples/bigquery/colab_quickstart_bigquery.ipynb",
    "docs/en/samples/looker/looker_gemini.md",
    "docs/en/samples/looker/looker_mcp_inspector.md",
    "docs/en/how-to/connect-ide/alloydb_pg_mcp.md",
    "docs/en/how-to/connect-ide/alloydb_pg_admin_mcp.md",
    "docs/en/how-to/connect-ide/bigquery_mcp.md",
    "docs/en/how-to/connect-ide/cloud_sql_pg_mcp.md",
    "docs/en/how-to/connect-ide/cloud_sql_mssql_mcp.md",
    "docs/en/how-to/connect-ide/cloud_sql_mysql_mcp.md",
    "docs/en/how-to/connect-ide/firestore_mcp.md",
    "docs/en/how-to/connect-ide/looker_mcp.md",
    "docs/en/how-to/connect-ide/postgres_mcp.md",
    "docs/en/how-to/connect-ide/spanner_mcp.md",
]
