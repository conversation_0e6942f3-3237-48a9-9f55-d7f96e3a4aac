<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>発票PDFジェネレーター</title>
    <!-- jsPDF ライブラリ -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <!-- PDF.js ライブラリ -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .file-management-section {
            margin-bottom: 40px;
        }

        .file-area {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            min-height: 200px;
        }

        .file-area:hover {
            border-color: #4facfe;
            background-color: #f8f9ff;
        }

        .file-area.dragover {
            border-color: #4facfe;
            background-color: #e3f2fd;
        }

        .file-area.has-files {
            border-style: solid;
            border-color: #4facfe;
            background-color: #f8f9ff;
            cursor: default;
        }

        .upload-prompt {
            text-align: center;
            padding: 40px 20px;
        }

        .upload-icon {
            font-size: 3em;
            color: #ddd;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 15px;
        }

        .file-input {
            display: none;
        }

        .files-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .file-item {
            border: 2px solid #eee;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            background: white;
            position: relative;
        }

        .file-item.empty {
            border-style: dashed;
            color: #999;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 150px;
        }

        .file-item .remove-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #f44336;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            font-size: 12px;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .preview-image {
            max-width: 100%;
            max-height: 120px;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .file-name {
            font-size: 0.8em;
            color: #666;
            word-break: break-all;
            margin-bottom: 5px;
        }

        .actions {
            text-align: center;
            margin-top: 30px;
        }

        .progress {
            width: 100%;
            height: 6px;
            background: #eee;
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .status {
            text-align: center;
            margin: 20px 0;
            font-weight: bold;
        }

        .success {
            color: #4caf50;
        }

        .error {
            color: #f44336;
        }

        @media (max-width: 768px) {
            .files-grid {
                grid-template-columns: 1fr;
            }

            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 発票PDFジェネレーター</h1>
            <p>1-2枚の発票をA4縦向きPDF（裁断線付き）に変換します</p>
        </div>

        <div class="main-content">
            <!-- ファイル管理セクション -->
            <div class="file-management-section">
                <h2>📁 発票ファイル管理</h2>
                <div class="file-area" id="fileArea">
                    <div class="upload-prompt" id="uploadPrompt">
                        <div class="upload-icon">📤</div>
                        <div class="upload-text">1-2枚の発票を選択するか、ドラッグ＆ドロップしてください</div>
                        <div style="font-size: 0.9em; color: #999;">対応形式: JPG, PNG, GIF, PDF (1-2ファイル選択可能)</div>
                        <button class="btn" style="margin-top: 15px;" onclick="document.getElementById('fileInput').click()">
                            📁 発票を選択
                        </button>
                    </div>
                    <div class="files-grid" id="filesGrid" style="display: none;">
                        <div class="file-item empty" id="fileSlot1">
                            <div style="color: #ccc; font-size: 2em;">📄</div>
                            <div>発票1をアップロード</div>
                        </div>
                        <div class="file-item empty" id="fileSlot2">
                            <div style="color: #ccc; font-size: 2em;">📄</div>
                            <div>発票2をアップロード</div>
                        </div>
                    </div>
                    <input type="file" id="fileInput" class="file-input" multiple accept="image/*,.pdf">
                </div>
            </div>

            <!-- 進捗バー -->
            <div class="progress" id="progressContainer" style="display: none;">
                <div class="progress-bar" id="progressBar"></div>
            </div>

            <!-- ステータス表示 -->
            <div class="status" id="status"></div>

            <!-- アクションボタン -->
            <div class="actions">
                <button class="btn" id="generateBtn" onclick="generatePDF()" disabled>
                    🔄 PDF生成
                </button>
                <button class="btn" id="downloadBtn" onclick="downloadPDF()" disabled>
                    💾 PDFダウンロード
                </button>
                <button class="btn" onclick="clearAll()">
                    🗑️ クリア
                </button>
            </div>
        </div>
    </div>

    <script>
        // グローバル変数
        let uploadedFiles = [];
        let generatedPDF = null;
        const maxFiles = 2;

        // DOM要素の取得
        const fileArea = document.getElementById('fileArea');
        const fileInput = document.getElementById('fileInput');
        const uploadPrompt = document.getElementById('uploadPrompt');
        const filesGrid = document.getElementById('filesGrid');
        const generateBtn = document.getElementById('generateBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const status = document.getElementById('status');

        // ドラッグ＆ドロップイベントの設定
        fileArea.addEventListener('dragover', handleDragOver);
        fileArea.addEventListener('dragleave', handleDragLeave);
        fileArea.addEventListener('drop', handleDrop);
        fileArea.addEventListener('click', handleFileAreaClick);

        // ファイル選択イベント
        fileInput.addEventListener('change', handleFileSelect);

        /**
         * ファイルエリアクリック処理
         */
        function handleFileAreaClick(e) {
            // ファイルが既にある場合は、空のスロットをクリックした時のみファイル選択
            if (uploadedFiles.length < maxFiles) {
                fileInput.click();
            }
        }

        /**
         * ドラッグオーバー処理
         */
        function handleDragOver(e) {
            e.preventDefault();
            if (uploadedFiles.length < maxFiles) {
                fileArea.classList.add('dragover');
            }
        }

        /**
         * ドラッグリーブ処理
         */
        function handleDragLeave(e) {
            e.preventDefault();
            fileArea.classList.remove('dragover');
        }

        /**
         * ドロップ処理
         */
        function handleDrop(e) {
            e.preventDefault();
            fileArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            processFiles(files);
        }

        /**
         * ファイル選択処理
         */
        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            processFiles(files);
        }

        /**
         * ファイル処理
         */
        function processFiles(files) {
            // ファイル数制限チェック
            if (uploadedFiles.length + files.length > maxFiles) {
                showStatus(`最大${maxFiles}ファイルまでアップロード可能です`, 'error');
                return;
            }

            // ファイル形式チェック
            const validFiles = files.filter(file => {
                const isImage = file.type.startsWith('image/');
                const isPDF = file.type === 'application/pdf';
                return isImage || isPDF;
            });

            if (validFiles.length !== files.length) {
                showStatus('対応していないファイル形式が含まれています', 'error');
                return;
            }

            // ファイルを追加
            validFiles.forEach(file => {
                if (uploadedFiles.length < maxFiles) {
                    uploadedFiles.push(file);
                }
            });

            updateFileDisplay();
            updateUI();
        }

        /**
         * ファイルプレビュー表示
         */
        async function displayFilePreview(file, index) {
            const fileSlot = document.getElementById(`fileSlot${index + 1}`);

            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    fileSlot.className = 'file-item';
                    fileSlot.innerHTML = `
                        <button class="remove-btn" onclick="removeFile(${index})">×</button>
                        <img src="${e.target.result}" class="preview-image" alt="プレビュー">
                        <div class="file-name">${file.name}</div>
                    `;
                };
                reader.readAsDataURL(file);
            } else if (file.type === 'application/pdf') {
                // PDFの場合、最初のページをプレビューとして表示
                try {
                    fileSlot.className = 'file-item';
                    fileSlot.innerHTML = `
                        <button class="remove-btn" onclick="removeFile(${index})">×</button>
                        <div style="color: #4facfe; font-size: 2em;">⏳</div>
                        <div>PDF読み込み中...</div>
                    `;

                    const imageData = await pdfToImage(file);
                    fileSlot.innerHTML = `
                        <button class="remove-btn" onclick="removeFile(${index})">×</button>
                        <img src="${imageData}" class="preview-image" alt="PDFプレビュー">
                        <div class="file-name">${file.name}</div>
                    `;
                } catch (error) {
                    console.error('PDFプレビューエラー:', error);
                    fileSlot.innerHTML = `
                        <button class="remove-btn" onclick="removeFile(${index})">×</button>
                        <div style="color: #f44336; font-size: 2em;">❌</div>
                        <div class="file-name">${file.name}</div>
                        <div style="font-size: 0.8em; color: #f44336;">プレビューエラー</div>
                    `;
                }
            }
        }

        /**
         * ファイル削除
         */
        function removeFile(index) {
            uploadedFiles.splice(index, 1);
            updateFileDisplay();
            updateUI();
        }

        /**
         * ファイル表示更新
         */
        function updateFileDisplay() {
            // ファイルスロットをリセット
            for (let i = 0; i < maxFiles; i++) {
                const fileSlot = document.getElementById(`fileSlot${i + 1}`);
                if (i < uploadedFiles.length) {
                    displayFilePreview(uploadedFiles[i], i);
                } else {
                    fileSlot.className = 'file-item empty';
                    fileSlot.innerHTML = `
                        <div style="color: #ccc; font-size: 2em;">📄</div>
                        <div>発票${i + 1}をアップロード</div>
                    `;
                }
            }

            // 表示モードを切り替え
            if (uploadedFiles.length > 0) {
                uploadPrompt.style.display = 'none';
                filesGrid.style.display = 'grid';
                fileArea.classList.add('has-files');
            } else {
                uploadPrompt.style.display = 'block';
                filesGrid.style.display = 'none';
                fileArea.classList.remove('has-files');
            }
        }

        /**
         * UI更新
         */
        function updateUI() {
            generateBtn.disabled = uploadedFiles.length === 0;

            if (uploadedFiles.length === maxFiles) {
                showStatus('2枚の発票アップロード完了！PDF生成ボタンをクリックしてください', 'success');
            } else if (uploadedFiles.length === 1) {
                showStatus('1枚の発票アップロード完了！PDF生成可能です（もう1枚追加も可能）', 'success');
            } else if (uploadedFiles.length > 0) {
                showStatus(`${uploadedFiles.length}/${maxFiles} ファイルアップロード済み`, '');
            } else {
                showStatus('ファイルをアップロードしてください', '');
            }
        }

        /**
         * ステータス表示
         */
        function showStatus(message, type = '') {
            status.textContent = message;
            status.className = `status ${type}`;
        }

        /**
         * 進捗表示
         */
        function showProgress(percent) {
            progressContainer.style.display = 'block';
            progressBar.style.width = percent + '%';
        }

        /**
         * 進捗非表示
         */
        function hideProgress() {
            progressContainer.style.display = 'none';
            progressBar.style.width = '0%';
        }

        /**
         * PDF生成
         */
        async function generatePDF() {
            if (uploadedFiles.length === 0) {
                showStatus('少なくとも1つのファイルをアップロードしてください', 'error');
                return;
            }

            try {
                showStatus('PDF生成中...', '');
                showProgress(10);

                // jsPDFインスタンス作成（A4縦向き）
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF({
                    orientation: 'portrait',
                    unit: 'mm',
                    format: 'a4'
                });

                // A4縦向きサイズ: 210mm x 297mm
                const pageWidth = 210;
                const pageHeight = 297;
                const margin = 10;
                const imageWidth = pageWidth - margin * 2; // 画像の最大幅
                const imageHeight = (pageHeight - margin * 3) / 2; // 2つの画像を上下に配置

                showProgress(30);

                // 各ファイルを処理
                for (let i = 0; i < uploadedFiles.length; i++) {
                    const file = uploadedFiles[i];
                    // 上下配置：1枚目は上半分、2枚目は下半分
                    const yPosition = margin + (imageHeight + margin) * i;

                    let imageData;

                    if (file.type.startsWith('image/')) {
                        imageData = await fileToBase64(file);
                    } else if (file.type === 'application/pdf') {
                        // PDFファイルを画像に変換
                        try {
                            imageData = await pdfToImage(file);
                        } catch (error) {
                            console.error('PDF変換エラー:', error);
                            showStatus(`PDF変換エラー: ${file.name}`, 'error');
                            return;
                        }
                    }

                    if (imageData) {
                        // 画像のアスペクト比を保持して配置
                        const img = new Image();
                        img.src = imageData;

                        await new Promise(resolve => {
                            img.onload = () => {
                                const aspectRatio = img.width / img.height;
                                let finalWidth = imageWidth;
                                let finalHeight = imageHeight;

                                // アスペクト比を保持しながら最適なサイズを計算
                                if (aspectRatio > imageWidth / imageHeight) {
                                    // 横長の画像：幅を基準にサイズ調整
                                    finalHeight = imageWidth / aspectRatio;
                                } else {
                                    // 縦長の画像：高さを基準にサイズ調整
                                    finalWidth = imageHeight * aspectRatio;
                                }

                                // 中央配置
                                const centerX = margin + (imageWidth - finalWidth) / 2;
                                const centerY = yPosition + (imageHeight - finalHeight) / 2;

                                pdf.addImage(imageData, 'JPEG', centerX, centerY, finalWidth, finalHeight);
                                resolve();
                            };
                        });
                    }

                    showProgress(30 + (i + 1) * 30);
                }

                showProgress(90);

                // 裁断線を追加
                addCutLines(pdf, pageWidth, pageHeight, margin, uploadedFiles.length);

                // PDF生成完了
                generatedPDF = pdf;
                downloadBtn.disabled = false;

                showProgress(100);
                showStatus('PDF生成完了！ダウンロードボタンをクリックしてください', 'success');

                setTimeout(hideProgress, 1000);

            } catch (error) {
                console.error('PDF生成エラー:', error);
                showStatus('PDF生成中にエラーが発生しました', 'error');
                hideProgress();
            }
        }

        /**
         * 裁断線を追加
         */
        function addCutLines(pdf, pageWidth, pageHeight, margin, fileCount) {
            // 線の設定
            pdf.setDrawColor(128, 128, 128); // グレー色
            pdf.setLineWidth(0.2); // 細い線

            const dashPattern = [2, 2]; // 破線パターン [線の長さ, 空白の長さ]

            // 上部裁断線（ページ上端から少し下）
            drawDashedLine(pdf, 0, 10, pageWidth, 10, dashPattern);

            // 中央裁断線（常に表示）
            const centerY = pageHeight / 2;
            drawDashedLine(pdf, 0, centerY, pageWidth, centerY, dashPattern);

            // 下部裁断線（ページ下端から少し上）
            drawDashedLine(pdf, 0, pageHeight - 10, pageWidth, pageHeight - 10, dashPattern);
        }

        /**
         * 破線を描画
         */
        function drawDashedLine(pdf, x1, y1, x2, y2, dashPattern) {
            const dx = x2 - x1;
            const dy = y2 - y1;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const unitX = dx / distance;
            const unitY = dy / distance;

            let currentX = x1;
            let currentY = y1;
            let remainingDistance = distance;
            let dashIndex = 0;
            let isDash = true;

            while (remainingDistance > 0) {
                const dashLength = Math.min(dashPattern[dashIndex % dashPattern.length], remainingDistance);

                if (isDash) {
                    const endX = currentX + unitX * dashLength;
                    const endY = currentY + unitY * dashLength;
                    pdf.line(currentX, currentY, endX, endY);
                }

                currentX += unitX * dashLength;
                currentY += unitY * dashLength;
                remainingDistance -= dashLength;

                dashIndex++;
                isDash = !isDash;
            }
        }

        /**
         * ファイルをBase64に変換
         */
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        /**
         * PDFダウンロード
         */
        function downloadPDF() {
            if (!generatedPDF) {
                showStatus('先にPDFを生成してください', 'error');
                return;
            }

            try {
                const currentDate = new Date();
                const dateString = currentDate.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');
                const filename = `発票合併_${dateString}.pdf`;

                // PDFのサイズをチェック
                const pdfOutput = generatedPDF.output('blob');
                console.log('PDF サイズ:', (pdfOutput.size / 1024 / 1024).toFixed(2), 'MB');

                if (pdfOutput.size > 50 * 1024 * 1024) { // 50MB以上の場合
                    showStatus('PDFファイルが大きすぎます。画像品質を下げて再生成してください', 'error');
                    return;
                }

                generatedPDF.save(filename);
                showStatus('PDFダウンロード完了！', 'success');
            } catch (error) {
                console.error('ダウンロードエラー:', error);
                showStatus(`ダウンロードエラー: ${error.message}`, 'error');
            }
        }

        /**
         * 全てクリア
         */
        function clearAll() {
            uploadedFiles = [];
            generatedPDF = null;
            fileInput.value = '';

            // ファイル表示をリセット
            updateFileDisplay();

            // ボタン状態をリセット
            generateBtn.disabled = true;
            downloadBtn.disabled = true;

            // ステータスをクリア
            showStatus('ファイルをアップロードしてください', '');
            hideProgress();
        }

        /**
         * PDFファイルを画像に変換
         */
        async function pdfToImage(file, scale = 1.5) {
            return new Promise(async (resolve, reject) => {
                try {
                    const arrayBuffer = await file.arrayBuffer();
                    const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
                    const page = await pdf.getPage(1); // 最初のページを取得

                    const viewport = page.getViewport({ scale: scale }); // スケールを調整可能に
                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');

                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    const renderContext = {
                        canvasContext: context,
                        viewport: viewport
                    };

                    await page.render(renderContext).promise;

                    // CanvasをBase64画像データに変換（品質を調整）
                    const imageData = canvas.toDataURL('image/jpeg', 0.7);

                    // 画像サイズをチェック（5MB以上の場合は品質を下げる）
                    if (imageData.length > 5 * 1024 * 1024) {
                        const lowerQualityData = canvas.toDataURL('image/jpeg', 0.5);
                        resolve(lowerQualityData);
                    } else {
                        resolve(imageData);
                    }
                } catch (error) {
                    console.error('PDF変換エラー:', error);
                    reject(error);
                }
            });
        }

        // PDF.js ワーカーの設定
        if (typeof pdfjsLib !== 'undefined') {
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
        }

        // 初期化
        document.addEventListener('DOMContentLoaded', function() {
            showStatus('ファイルをアップロードしてください', '');
        });
    </script>
</body>
</html>
