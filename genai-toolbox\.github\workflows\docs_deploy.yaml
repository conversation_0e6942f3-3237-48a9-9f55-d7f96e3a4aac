# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: "docs"

permissions:
  contents: write

on:
  push:
    branches:
      - main
    paths:
      - 'docs/**'
      - 'github/workflows/docs**'
      - '.hugo/**'

  # Allow triggering manually.
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-24.04
    defaults:
      run:
        working-directory: .hugo
    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}
      cancel-in-progress: true
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          fetch-depth: 0 # Fetch all history for .GitInfo and .Lastmod

      - name: Setup <PERSON>
        uses: peaceiris/actions-hugo@75d2e84710de30f6ff7268e08f310b60ef14033f # v3
        with:
          hugo-version: "0.145.0"
          extended: true

      - name: Setup Node
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4
        with:
          node-version: "22"

      - name: Cache dependencies
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - run: npm ci
      - run: hugo --minify
        env:
          HUGO_BASEURL: https://${{ github.repository_owner }}.github.io/${{ github.event.repository.name }}/
          HUGO_RELATIVEURLS: false

      - name: Deploy
        uses: peaceiris/actions-gh-pages@4f9cc6602d3f66b9c108549d475ec49e8ef4d45e # v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: .hugo/public
          # Do not delete previews on each production deploy.
          # CSS or JS changes will require manual clean-up.
          keep_files: true
          commit_message: "deploy: ${{ github.event.head_commit.message }}"
