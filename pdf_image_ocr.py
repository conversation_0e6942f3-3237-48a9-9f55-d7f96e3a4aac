#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF图片内容解析工具
支持扫描版PDF和图片PDF的文字提取
"""

import os
import sys
from pathlib import Path
import tempfile
import shutil

def check_dependencies():
    """检查必要的依赖库"""
    required_packages = {
        'fitz': 'PyMuPDF',
        'pdf2image': 'pdf2image', 
        'PIL': 'Pillow',
        'cv2': 'opencv-python',
        'easyocr': 'easyocr'
    }
    
    missing_packages = []
    
    for package, install_name in required_packages.items():
        try:
            if package == 'PIL':
                import PIL
            elif package == 'cv2':
                import cv2
            elif package == 'fitz':
                import fitz
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(install_name)
    
    if missing_packages:
        print(f"缺少依赖库: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    return True

class PDFImageOCR:
    def __init__(self, use_gpu=False):
        """初始化PDF图片OCR处理器"""
        self.ocr_reader = None
        self.use_gpu = use_gpu
        self.temp_dir = None
        
    def init_ocr(self):
        """初始化OCR引擎"""
        if self.ocr_reader is None:
            try:
                import easyocr
                print("初始化OCR引擎...")
                self.ocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=self.use_gpu)
                print("OCR引擎初始化完成")
            except Exception as e:
                print(f"OCR引擎初始化失败: {e}")
                return False
        return True
    
    def check_pdf_type(self, pdf_path):
        """检查PDF类型：文本PDF还是图片PDF"""
        try:
            import fitz
            doc = fitz.open(pdf_path)
            
            text_pages = 0
            image_pages = 0
            
            for page_num in range(min(5, len(doc))):  # 检查前5页
                page = doc[page_num]
                text = page.get_text().strip()
                images = page.get_images()
                
                if len(text) > 100:  # 如果有较多文字
                    text_pages += 1
                if len(images) > 0:  # 如果有图片
                    image_pages += 1
            
            doc.close()
            
            if text_pages > image_pages:
                return "text_pdf"
            elif image_pages > 0:
                return "image_pdf"
            else:
                return "mixed_pdf"
                
        except Exception as e:
            print(f"检查PDF类型时出错: {e}")
            return "unknown"
    
    def extract_text_from_text_pdf(self, pdf_path):
        """从文本PDF提取文字"""
        try:
            import fitz
            doc = fitz.open(pdf_path)
            
            all_text = []
            for page_num in range(len(doc)):
                page = doc[page_num]
                text = page.get_text()
                
                if text.strip():
                    page_text = f"\n## 第 {page_num + 1} 页\n\n{text}\n\n---\n"
                    all_text.append(page_text)
            
            doc.close()
            return "".join(all_text)
            
        except Exception as e:
            print(f"提取文本PDF内容时出错: {e}")
            return ""
    
    def pdf_to_images(self, pdf_path, output_dir):
        """将PDF转换为图片"""
        try:
            from pdf2image import convert_from_path
            
            print(f"将PDF转换为图片...")
            
            # 转换PDF为图片
            images = convert_from_path(
                pdf_path,
                dpi=300,  # 高分辨率
                fmt='PNG'
            )
            
            image_paths = []
            for i, image in enumerate(images):
                image_path = os.path.join(output_dir, f"page_{i+1:03d}.png")
                image.save(image_path, 'PNG')
                image_paths.append(image_path)
            
            print(f"转换完成，共 {len(images)} 页")
            return image_paths
            
        except Exception as e:
            print(f"PDF转图片时出错: {e}")
            return []
    
    def extract_images_from_pdf(self, pdf_path, output_dir):
        """从PDF中提取嵌入的图片"""
        try:
            import fitz
            doc = fitz.open(pdf_path)
            
            image_paths = []
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                images = page.get_images()
                
                for img_index, img in enumerate(images):
                    # 获取图片数据
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)
                    
                    if pix.n - pix.alpha < 4:  # 确保不是CMYK
                        image_path = os.path.join(
                            output_dir, 
                            f"page_{page_num+1:03d}_img_{img_index+1}.png"
                        )
                        pix.save(image_path)
                        image_paths.append(image_path)
                    
                    pix = None
            
            doc.close()
            return image_paths
            
        except Exception as e:
            print(f"提取PDF图片时出错: {e}")
            return []
    
    def ocr_image(self, image_path):
        """对单张图片进行OCR"""
        try:
            if not self.init_ocr():
                return ""
            
            results = self.ocr_reader.readtext(image_path)
            
            # 提取文字，按位置排序
            text_blocks = []
            for (bbox, text, confidence) in results:
                if confidence > 0.3:
                    y_coord = bbox[0][1]
                    text_blocks.append((y_coord, text.strip()))
            
            # 按y坐标排序
            text_blocks.sort(key=lambda x: x[0])
            
            return '\n'.join([text for _, text in text_blocks])
            
        except Exception as e:
            print(f"OCR处理图片时出错 {image_path}: {e}")
            return ""
    
    def process_pdf(self, pdf_path, output_file):
        """处理PDF文件"""
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            print(f"PDF文件不存在: {pdf_path}")
            return False
        
        print(f"开始处理PDF: {pdf_path.name}")
        
        # 检查PDF类型
        pdf_type = self.check_pdf_type(pdf_path)
        print(f"PDF类型: {pdf_type}")
        
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        try:
            all_text = []
            
            if pdf_type == "text_pdf":
                # 直接提取文字
                print("检测到文本PDF，直接提取文字...")
                text = self.extract_text_from_text_pdf(pdf_path)
                all_text.append(text)
                
            else:
                # 图片PDF，需要OCR
                print("检测到图片PDF，使用OCR提取文字...")
                
                # 方法1：将PDF页面转换为图片
                image_paths = self.pdf_to_images(pdf_path, self.temp_dir)
                
                if not image_paths:
                    # 方法2：提取PDF中的嵌入图片
                    print("尝试提取PDF中的嵌入图片...")
                    image_paths = self.extract_images_from_pdf(pdf_path, self.temp_dir)
                
                if image_paths:
                    print(f"开始OCR处理 {len(image_paths)} 张图片...")
                    
                    for i, image_path in enumerate(image_paths, 1):
                        print(f"处理进度: {i}/{len(image_paths)}")
                        
                        text = self.ocr_image(image_path)
                        
                        page_text = f"\n## 第 {i} 页\n\n{text if text.strip() else '未检测到文字内容'}\n\n---\n"
                        all_text.append(page_text)
                else:
                    print("未能提取到图片")
                    return False
            
            # 保存结果
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"# PDF文字提取结果 - {pdf_path.name}\n\n")
                f.write(f"PDF类型: {pdf_type}\n")
                f.write(f"处理时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write("".join(all_text))
            
            print(f"✅ 处理完成！结果已保存到: {output_file}")
            return True
            
        finally:
            # 清理临时文件
            if self.temp_dir and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)

def main():
    """主函数"""
    if not check_dependencies():
        return
    
    # 获取PDF文件路径
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    else:
        pdf_path = input("请输入PDF文件路径: ").strip().strip('"')
    
    if not os.path.exists(pdf_path):
        print(f"文件不存在: {pdf_path}")
        return
    
    # 设置输出文件
    pdf_name = Path(pdf_path).stem
    output_file = f"{pdf_name}_extracted_text.md"
    
    # 创建处理器
    processor = PDFImageOCR(use_gpu=False)  # 使用CPU
    
    # 处理PDF
    success = processor.process_pdf(pdf_path, output_file)
    
    if success:
        print(f"文字提取完成，结果保存在: {output_file}")
    else:
        print("文字提取失败")

if __name__ == "__main__":
    main()
