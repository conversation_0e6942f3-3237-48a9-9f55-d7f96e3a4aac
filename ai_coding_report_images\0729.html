<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Chosen Palette: Warm Neutrals & Slate Blue -->
    <!-- Application Structure Plan: The application is designed as a single-page interactive dashboard, moving away from the linear format of the source report. The structure includes a sticky header for navigation, a main overview section with key market statistics (adoptions, ARR, funding), and a central, tab-based interactive section for the "Seven Great Debates" (非共识). This thematic, non-linear structure was chosen to allow users to freely explore topics of interest, compare different viewpoints, and digest the dense information in a more engaging and user-friendly manner. The goal is to facilitate understanding and discovery rather than simple reading. -->
    <!-- Visualization & Content Choices: The application uses Chart.js for all data visualizations (bar, doughnut charts) to present quantitative data interactively. Goal: Compare/Inform -> Viz: Interactive charts with tooltips -> Interaction: Hover to see details, clickable tabs to switch contexts -> Justification: Charts provide a quick, clear comparison of market data (ARR, adoption rates), superior to static tables. The "Seven Debates" are organized into a tabbed interface. Goal: Organize/Compare -> Viz: HTML/Tailwind structured cards and text blocks -> Interaction: Clicking tabs reveals detailed content for each debate -> Justification: This organizes the core arguments of the report into digestible, parallel tracks, allowing users to focus on one debate at a time without losing context. All diagrams (e.g., Software 1.0->3.0) are built with semantic HTML and styled with Tailwind. -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <title>AI Coding 行业全景交互式报告</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        html {
            scroll-behavior: smooth;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f8f7f4;
            color: #333;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
        .nav-link {
            transition: all 0.3s;
            border-bottom: 2px solid transparent;
        }
        .nav-link:hover, .nav-link.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }
        .tab-button {
            transition: all 0.3s;
        }
        .tab-button.active {
            background-color: #3b82f6;
            color: white;
            transform: scale(1.05);
        }
        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(15px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .section-card {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .section-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.1);
        }
    </style>
</head>
<body class="antialiased">

    <header id="header" class="bg-white/80 backdrop-blur-lg sticky top-0 z-50 shadow-md">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <h1 class="text-xl md:text-2xl font-bold text-gray-800">AI Coding 行业全景</h1>
            <div class="hidden md:flex space-x-8">
                <a href="#market-overview" class="nav-link font-medium text-gray-600 pb-1">市场概览</a>
                <a href="#great-debates" class="nav-link font-medium text-gray-600 pb-1">七大非共识</a>
                <a href="#future-outlook" class="nav-link font-medium text-gray-600 pb-1">未来展望</a>
            </div>
            <div class="md:hidden">
                <button id="mobile-menu-button" class="text-gray-800 focus:outline-none">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
                </button>
            </div>
        </nav>
        <div id="mobile-menu" class="hidden md:hidden px-6 pb-4">
            <a href="#market-overview" class="block py-2 text-gray-600 nav-link-mobile">市场概览</a>
            <a href="#great-debates" class="block py-2 text-gray-600 nav-link-mobile">七大非共识</a>
            <a href="#future-outlook" class="block py-2 text-gray-600 nav-link-mobile">未来展望</a>
        </div>
    </header>

    <main class="container mx-auto px-6 py-12">
        <section id="hero" class="text-center mb-20">
            <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">超越软件的编程范式革命</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                AI 正在颠覆编程的核心定义，将其从单纯的“编码”提升到“表达意图”和“实现愿景”的更高维度。本报告将带您深入探索这场由AI驱动的行业变革，洞察市场脉搏，剖析核心争议，并展望未来的丰饶时代。
            </p>
        </section>

        <section id="market-overview" class="mb-20">
            <h2 class="text-3xl font-bold text-center mb-12">市场概览：爆发式增长与广泛渗透</h2>
            <div class="grid md:grid-cols-2 gap-8">
                <div class="section-card p-6">
                    <h3 class="text-xl font-bold mb-4 text-center">C端渗透率：AI编程仅次于写作</h3>
                    <p class="text-sm text-gray-500 mb-4 text-center">根据Menlo Ventures对美国成年人的调研，47%的受访者已在日常或工作中使用AI进行编程，其广泛接受度仅次于写作支持。</p>
                    <div class="chart-container h-64 md:h-80">
                        <canvas id="c-adoption-chart"></canvas>
                    </div>
                </div>
                <div class="section-card p-6">
                    <h3 class="text-xl font-bold mb-4 text-center">B端落地：编程辅助断层式领先</h3>
                     <p class="text-sm text-gray-500 mb-4 text-center">在企业级应用中，编程辅助以77%的普及度遥遥领先，成为当前最快落地、影响力最大的AI应用方向。</p>
                    <div class="chart-container h-64 md:h-80">
                        <canvas id="b-adoption-chart"></canvas>
                    </div>
                </div>
                <div class="section-card p-6 md:col-span-2">
                    <h3 class="text-xl font-bold mb-4 text-center">企业ARR（年度经常性收入）记录屡被刷新</h3>
                    <p class="text-sm text-gray-500 mb-4 text-center">AI Coding公司正以惊人的速度增长，Cursor用时12个月ARR从100万增至1亿美元，多家公司在短时间内实现数倍甚至十倍的增长。</p>
                    <div class="chart-container h-80 md:h-96">
                        <canvas id="arr-chart"></canvas>
                    </div>
                </div>
                 <div class="section-card p-6 md:col-span-2">
                    <h3 class="text-xl font-bold mb-4 text-center">融资与估值：独角兽扎堆，资本热捧</h3>
                    <p class="text-sm text-gray-500 mb-4 text-center">AI Coding已成为最具商业价值的赛道之一。下表展示了部分头部公司的融资情况，其中Anysphere (Cursor) 在2025年6月的C轮融资中估值高达99亿美元。</p>
                    <div class="overflow-x-auto mt-4">
                        <table class="w-full text-sm text-left text-gray-500">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3">公司</th>
                                    <th scope="col" class="px-6 py-3">轮次</th>
                                    <th scope="col" class="px-6 py-3">融资金额</th>
                                    <th scope="col" class="px-6 py-3">估值</th>
                                    <th scope="col" class="px-6 py-3">创立时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="bg-white border-b"><td class="px-6 py-4 font-medium">Anysphere (Cursor)</td><td class="px-6 py-4">C轮</td><td class="px-6 py-4">9亿美元</td><td class="px-6 py-4">99亿美元</td><td class="px-6 py-4">2022</td></tr>
                                <tr class="bg-gray-50 border-b"><td class="px-6 py-4 font-medium">Poolside AI</td><td class="px-6 py-4">B轮</td><td class="px-6 py-4">5亿美元</td><td class="px-6 py-4">30亿美元</td><td class="px-6 py-4">2023</td></tr>
                                <tr class="bg-white border-b"><td class="px-6 py-4 font-medium">Cognition AI (Devin)</td><td class="px-6 py-4">A+轮</td><td class="px-6 py-4">1.75亿美元</td><td class="px-6 py-4">20亿美元</td><td class="px-6 py-4">2023</td></tr>
                                <tr class="bg-gray-50 border-b"><td class="px-6 py-4 font-medium">Lovable</td><td class="px-6 py-4">A轮</td><td class="px-6 py-4">2亿美元</td><td class="px-6 py-4">18亿美元</td><td class="px-6 py-4">2023</td></tr>
                                <tr class="bg-white"><td class="px-6 py-4 font-medium">Replit</td><td class="px-6 py-4">B+轮</td><td class="px-6 py-4">9740万美元</td><td class="px-6 py-4">11.6亿美元</td><td class="px-6 py-4">2016</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <section id="great-debates" class="mb-20">
            <h2 class="text-3xl font-bold text-center mb-4">七大非共识：行业发展的核心争议</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto text-center mb-12">在AI Coding高速发展的共识之下，行业在产品形态、商业模式、价值主张等七个核心方向上，仍然存在着巨大的“非共识”。这些争议点，正是理解行业未来的关键。</p>
            
            <div class="flex flex-wrap justify-center gap-2 md:gap-4 mb-8" id="tabs-buttons">
                <button data-tab="tab1" class="tab-button px-4 py-2 text-sm font-medium bg-white rounded-full shadow-sm active">产品形态</button>
                <button data-tab="tab2" class="tab-button px-4 py-2 text-sm font-medium bg-white rounded-full shadow-sm">模型选择</button>
                <button data-tab="tab3" class="tab-button px-4 py-2 text-sm font-medium bg-white rounded-full shadow-sm">用户价值</button>
                <button data-tab="tab4" class="tab-button px-4 py-2 text-sm font-medium bg-white rounded-full shadow-sm">付费模式</button>
                <button data-tab="tab5" class="tab-button px-4 py-2 text-sm font-medium bg-white rounded-full shadow-sm">企业态度</button>
                <button data-tab="tab6" class="tab-button px-4 py-2 text-sm font-medium bg-white rounded-full shadow-sm">组织影响</button>
                <button data-tab="tab7" class="tab-button px-4 py-2 text-sm font-medium bg-white rounded-full shadow-sm">未来格局</button>
            </div>

            <div id="tabs-content" class="section-card p-6 md:p-8 min-h-[300px]">
                <div id="tab1" class="tab-content fade-in">
                    <h3 class="text-2xl font-bold mb-4">非共识01: 最佳产品形态 (本地 VS 云端)</h3>
                    <p class="mb-6">AI Coding产品主要分为两大阵营：贴近开发者传统工作流的“本地工具”和致力于降低门槛、人人可用的“云端产品”。两者各有优势，分别满足不同用户群体的需求。</p>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="font-bold text-lg mb-2">本地部署：解放生产力</h4>
                            <p class="text-sm text-gray-600 mb-3">开发者最熟悉、使用最灵活、不强依赖网络。主要形态包括IDE插件和命令行工具(CLI)。</p>
                            <p class="text-sm"><strong>代表:</strong> Cursor, Github Copilot, Claude Code</p>
                        </div>
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="font-bold text-lg mb-2">云端部署：人人可用</h4>
                            <p class="text-sm text-gray-600 mb-3">0本地配置、全栈开发、协作丝滑。主要形态包括Vibe Coding产品和异步Coding Agent。</p>
                            <p class="text-sm"><strong>代表:</strong> Lovable, Devin, Replit</p>
                        </div>
                    </div>
                </div>
                <div id="tab2" class="tab-content hidden fade-in">
                    <h3 class="text-2xl font-bold mb-4">非共识02: 模型选择 (自研 VS 第三方)</h3>
                    <p class="mb-6">产品高度依赖模型能力，但选择自研、第三方或混合策略，是各公司的核心战略抉择。Claude 3.5 Sonnet的出现成为行业PMF的临界点。</p>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-bold text-lg mb-2">策略分野</h4>
                            <ul class="list-disc list-inside text-gray-600 space-y-2">
                                <li><strong>深度自研:</strong> 以AGI为目标，追求极致性能，如Magic, Poolside。</li>
                                <li><strong>第三方多元模型:</strong> 追求“尽可能多和最好的智能”，快速响应市场，如Lovable, Github。</li>
                                <li><strong>混合策略:</strong> 结合外部模型与自研调优，兼顾速度与定制化，如Cursor, Replit。</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-bold text-lg mb-2">模型选择排序</h4>
                            <p class="text-sm text-gray-600 mb-3">在选择第三方模型时，Anthropic的Sonnet模型因其在速度、编辑能力和长上下文等方面的综合表现，被普遍选为最佳。</p>
                            <div class="chart-container h-64">
                                <canvas id="model-rank-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="tab3" class="tab-content hidden fade-in">
                    <h3 class="text-2xl font-bold mb-4">非共识03: 用户价值 (提效 VS 降效)</h3>
                    <p class="mb-6">AI Coding能显著提效是普遍认知，但关于其是否可能在某些方面“降效”或降低代码质量，行业内存在巨大争议。数据显示，开发者的“体感提效”与实际“生产力指标”可能存在偏差。</p>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-bold text-lg mb-2 text-green-800">提效论</h4>
                            <p class="text-sm text-gray-700">大公司评估提效10%-30%。个人开发者报告显示，任务完成速度提升55%，代码审查速度提升15%，满意度也显著提高。</p>
                            <blockquote class="mt-2 text-sm italic">“写原型验证代码时, 效率提升完全不是 50%, 而是至少 10 倍。” - 吴恩达</blockquote>
                        </div>
                        <div class="bg-red-50 p-4 rounded-lg">
                            <h4 class="font-bold text-lg mb-2 text-red-800">降效论</h4>
                            <p class="text-sm text-gray-700">METR实验显示生产力反而下降19%。DORA报告也指出AI采用可能对软件交付性能产生负面影响。开发者主要担忧信息误导、算法偏见和复杂任务处理能力不足。</p>
                             <blockquote class="mt-2 text-sm italic">“AI 工具可能会降低经验丰富的工程师的工作效率。” - GitHub CEO</blockquote>
                        </div>
                    </div>
                </div>
                <div id="tab4" class="tab-content hidden fade-in">
                    <h3 class="text-2xl font-bold mb-4">非共识04: 理想付费模式 (固定 VS 按需)</h3>
                    <p class="mb-6">由于API成本的“不可控性”，传统SaaS的固定订阅模式受到挑战。行业正从固定月费，走向“订阅+按需”的混合模式，并探索更抽象的计费单元。</p>
                    <div class="grid md:grid-cols-2 gap-6">
                         <div>
                            <h4 class="font-bold text-lg mb-2">四种主流付费模式</h4>
                            <ul class="list-disc list-inside text-gray-600 space-y-2 text-sm">
                                <li><strong>订阅制固定月费:</strong> 核心功能+额度，超额另计 (e.g., GitHub Copilot)。</li>
                                <li><strong>消费交互计费:</strong> 按消息/交互次数计费 (e.g., Lovable.dev)。</li>
                                <li><strong>基于Token/Credit计费:</strong> 预购额度，用完即止 (e.g., Bolt.new)。</li>
                                <li><strong>按需付费:</strong> 按实际资源/任务复杂度计费 (e.g., Devin)。</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-bold text-lg mb-2">定价模式分布</h4>
                            <p class="text-sm text-gray-600 mb-3">根据ICONIQ调查，混合模式已成为最主流选择，占比38%，超过了传统的订阅/席位制。</p>
                            <div class="chart-container h-64">
                                <canvas id="pricing-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="tab5" class="tab-content hidden fade-in">
                    <h3 class="text-2xl font-bold mb-4">非共识05: 大企业态度 (激进 VS 渐进)</h3>
                    <p class="mb-6">大企业在拥抱AI Coding上态度普遍积极，但具体策略上存在“激进”与“渐进”的差异。一方面，企业内部AI代码占比持续提升；另一方面，从强制使用到纳入绩效评估，企业正加快推动全面落地。</p>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                             <h4 class="font-bold text-lg mb-2">AI代码占比预测</h4>
                             <ul class="list-disc list-inside text-gray-600 space-y-2 text-sm">
                                 <li><strong>微软/谷歌:</strong> 已有约30%新代码由AI生成。</li>
                                 <li><strong>Meta:</strong> 预测2026年AI将承担50%编程工作。</li>
                                 <li><strong>Anthropic:</strong> 预测在12个月内，世界将进入AI编写几乎所有代码的时代。</li>
                             </ul>
                        </div>
                        <div>
                            <h4 class="font-bold text-lg mb-2">落地策略</h4>
                            <ul class="list-disc list-inside text-gray-600 space-y-2 text-sm">
                                 <li><strong>强制使用:</strong> Perplexity要求所有员工使用AI编码工具。</li>
                                 <li><strong>与产出挂钩:</strong> 亚马逊要求在人员缩减下保持产出不变，倒逼使用AI。</li>
                                 <li><strong>纳入绩效:</strong> 微软、Shopify、阿里等公司已将AI使用情况纳入绩效评估。</li>
                             </ul>
                        </div>
                    </div>
                </div>
                <div id="tab6" class="tab-content hidden fade-in">
                    <h3 class="text-2xl font-bold mb-4">非共识06: 组织影响 (裁员 VS 扩张)</h3>
                    <p class="mb-6">AI提效带来了“裁员潮”的担忧，但也催生了新的可能性。软件开发岗位需求出现结构性分化，初级岗位减少，高级岗位增加。同时，小规模、高效率的“AI原生团队”正成为创新主力。</p>
                     <div class="grid md:grid-cols-2 gap-6">
                        <div class="bg-red-50 p-4 rounded-lg">
                            <h4 class="font-bold text-lg mb-2 text-red-800">裁员与需求锐减</h4>
                            <p class="text-sm text-gray-700">软件开发岗位总数相比疫情前减少35%，初级工程师岗位比例从近30%降至20%以下。</p>
                            <blockquote class="mt-2 text-sm italic">“随着AI广泛应用, 公司整体员工数量将会减少。” - Amazon CEO</blockquote>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-bold text-lg mb-2 text-green-800">扩张与新机遇</h4>
                            <p class="text-sm text-gray-700">企业可以用更少资金雇佣更多懂AI协作的人。10人以下小团队正成为创新主力，70%的软件创新将源于此。</p>
                             <blockquote class="mt-2 text-sm italic">“一个由10名工程师组成的团队, 借助AI工具, 就能完成过去需要100名工程师才能完成的工作。” - 微软 CTO</blockquote>
                        </div>
                    </div>
                </div>
                <div id="tab7" class="tab-content hidden fade-in">
                    <h3 class="text-2xl font-bold mb-4">非共识07: 未来格局 (专业 VS 普惠)</h3>
                    <p class="mb-6">未来市场是继续服务于专业开发者，还是走向人人可编程的“普惠”时代？趋势表明，编程的核心正从“代码”转向“意图”，AI将赋能亿万消费者成为创造者，催生定制化、个性化应用的爆发。</p>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-bold text-lg mb-2">代码 ≠ 编程</h4>
                            <p class="text-sm text-gray-600">AI使编程高度抽象化，自然语言成为新范式。开发者的重心从语法实现转向更高层的“意图表达”和“问题分解”。</p>
                        </div>
                        <div>
                            <h4 class="font-bold text-lg mb-2">从2500万到10亿开发者</h4>
                            <p class="text-sm text-gray-600">AI正将编程能力从少数专业人士拓展到数十亿人。GitHub已将“培育10亿开发者”作为核心愿景。</p>
                        </div>
                        <div class="md:col-span-2">
                             <blockquote class="mt-4 text-center text-lg font-semibold text-blue-600 bg-blue-50 p-4 rounded-lg">“未来如果你能梦想它，你就能发布它。”<br><span class="text-sm font-medium text-gray-500">- v0, Guillermo Rauch</span></blockquote>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="future-outlook" class="text-center">
            <h2 class="text-3xl font-bold mb-4">丰饶时代的预演</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
                我们正处在一个转折点，软件开发的门槛被前所未有地降低。“品味”——即对构建什么以及如何构建的深刻理解和直觉——正取代纯粹的技术实现，成为核心竞争力。
            </p>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="section-card p-6">
                    <h3 class="text-xl font-bold mb-2">品味是核心</h3>
                    <p class="text-gray-600">当AI能轻松生成代码，区分产品的关键在于其背后的思想、审美和用户体验。</p>
                </div>
                <div class="section-card p-6">
                    <h3 class="text-xl font-bold mb-2">氛围编程 (Vibe Coding)</h3>
                    <p class="text-gray-600">一种全新的、沉浸式的编程方式，让创造者专注于“氛围”和“意图”，而非代码细节。</p>
                </div>
                <div class="section-card p-6">
                    <h3 class="text-xl font-bold mb-2">即刻效率</h3>
                    <p class="text-gray-600">定制化软件的成本和时间将极大缩减，催生大量个性化和微型应用的爆发，解锁万亿级市场。</p>
                </div>
            </div>
        </section>
    </main>

    <footer class="bg-gray-800 text-white mt-20">
        <div class="container mx-auto px-6 py-8 text-center">
            <p>本交互式报告基于《AI Coding行业全景概览》生成。</p>
            <p class="text-sm text-gray-400 mt-2">数据和观点仅供参考。最后更新于 2025年7月。</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Chart.js Global Config
            Chart.defaults.font.family = "'Noto Sans SC', sans-serif";
            Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            Chart.defaults.plugins.tooltip.titleFont = { size: 14, weight: 'bold' };
            Chart.defaults.plugins.tooltip.bodyFont = { size: 12 };
            Chart.defaults.plugins.tooltip.padding = 10;
            Chart.defaults.plugins.tooltip.cornerRadius = 4;

            const chartColors = {
                primary: 'rgba(59, 130, 246, 0.8)',
                primaryLight: 'rgba(96, 165, 250, 0.6)',
                secondary: 'rgba(239, 68, 68, 0.8)',
                gray: 'rgba(107, 114, 128, 0.5)',
                accent1: 'rgba(245, 158, 11, 0.8)',
                accent2: 'rgba(16, 185, 129, 0.8)',
                accent3: 'rgba(139, 92, 246, 0.8)',
            };

            // C-Adoption Chart
            const cAdoptionCtx = document.getElementById('c-adoption-chart');
            if(cAdoptionCtx) {
                new Chart(cAdoptionCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['写作支持', '编程项目', '作品辅导', '制作演说文稿', '其他'],
                        datasets: [{
                            label: 'AI使用场景渗透率',
                            data: [51, 47, 43, 38, 37],
                            backgroundColor: [chartColors.primary, chartColors.accent1, chartColors.accent2, chartColors.accent3, chartColors.gray],
                            borderColor: '#f8f7f4',
                            borderWidth: 3
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { position: 'bottom' } }
                    }
                });
            }

            // B-Adoption Chart
            const bAdoptionCtx = document.getElementById('b-adoption-chart');
            if(bAdoptionCtx) {
                new Chart(bAdoptionCtx, {
                    type: 'bar',
                    data: {
                        labels: ['编程/编码', '内容生成', '文档搜索', '产品设计', '数据分析'],
                        datasets: [{
                            label: '企业AI应用普及度 (%)',
                            data: [77, 65, 57, 56, 48],
                            backgroundColor: chartColors.primaryLight,
                            borderColor: chartColors.primary,
                            borderWidth: 1,
                            borderRadius: 4
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { display: false } },
                        scales: { x: { beginAtZero: true } }
                    }
                });
            }

            // ARR Chart
            const arrCtx = document.getElementById('arr-chart');
            if(arrCtx) {
                new Chart(arrCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Cursor', 'Github', 'Vercel', 'Windsurf', 'Replit', 'Lovable'],
                        datasets: [{
                            label: 'ARR (亿美元)',
                            data: [5, 4, 2, 1, 1, 0.75],
                            backgroundColor: [chartColors.primary, chartColors.accent1, chartColors.accent2, chartColors.accent3, chartColors.secondary, chartColors.gray],
                            borderRadius: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { display: false } },
                        scales: { y: { beginAtZero: true } }
                    }
                });
            }

            // Model Rank Chart
            const modelRankCtx = document.getElementById('model-rank-chart');
            if(modelRankCtx) {
                new Chart(modelRankCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Anthropic Sonnet', 'OpenAI', 'Google Gemini', 'DeepSeek'],
                        datasets: [{
                            label: '模型综合评分',
                            data: [5.2, 3.9, 3.8, 1.2],
                            backgroundColor: chartColors.primaryLight,
                            borderColor: chartColors.primary,
                            borderWidth: 1,
                            borderRadius: 4
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { display: false } },
                        scales: { x: { beginAtZero: true } }
                    }
                });
            }

            // Pricing Chart
            const pricingCtx = document.getElementById('pricing-chart');
            if(pricingCtx) {
                new Chart(pricingCtx, {
                    type: 'pie',
                    data: {
                        labels: ['混合模式', '订阅/席位制', '按使用量', '按结果付费'],
                        datasets: [{
                            label: '定价模式分布',
                            data: [38, 36, 19, 6],
                            backgroundColor: [chartColors.primary, chartColors.accent1, chartColors.accent2, chartColors.gray],
                            borderColor: '#ffffff',
                            borderWidth: 3
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { position: 'bottom' } }
                    }
                });
            }

            // Tab functionality
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');
                    
                    const tabId = button.dataset.tab;
                    tabContents.forEach(content => {
                        if (content.id === tabId) {
                            content.classList.remove('hidden');
                        } else {
                            content.classList.add('hidden');
                        }
                    });
                });
            });

            // Mobile menu functionality
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileNavLinks = document.querySelectorAll('.nav-link-mobile');
            mobileMenuButton.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
            mobileNavLinks.forEach(link => {
                link.addEventListener('click', () => {
                    mobileMenu.classList.add('hidden');
                });
            });
            
            // Fade-in animation on scroll
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in');
                    }
                });
            }, { threshold: 0.1 });

            document.querySelectorAll('section, .section-card').forEach(el => {
                observer.observe(el);
            });
        });
    </script>
</body>
</html>
