# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: "docs"

permissions:
  contents: write
  pull-requests: write

# This Workflow depends on 'github.event.number',
# not compatible with branch or manual triggers.
on:
  pull_request:
    types:
      - closed

jobs:
  clean:
    if: ${{ !github.event.pull_request.head.repo.fork }}
    runs-on: ubuntu-24.04
    concurrency:
      # Shared concurrency group wih preview staging.
      group: "preview-${{ github.event.number }}"
      cancel-in-progress: true
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          ref: gh-pages

      - name: Remove Preview
        run: |
          rm -Rf ./previews/PR-${{ github.event.number }}
          git config user.name 'github-actions[bot]'
          git config user.email 'github-actions[bot]@users.noreply.github.com'
          git add -u previews/PR-${{ github.event.number }}
          git commit --message "cleanup: previews/PR-${{ github.event.number }}"
          git push

      - name: Comment
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.payload.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: "🧨 Preview deployments removed."
            })