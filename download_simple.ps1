# AI Coding Report Images Download Script
Write-Host "AI Coding Report画像のダウンロードを開始します..."

# 画像URLとファイル名のペア
$images = @(
    @{url="https://developer.qcloudimg.com/http-save/10625241/8d2ec50881ffc7fc5b0bb7a746869b3a.webp"; name="page_01.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/0c126d077b70676966d0c042b66cf61a.webp"; name="page_02.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/d7103a06504107c7d54ccfcddea16dcf.webp"; name="page_03.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/c59ffd426845763234792da617e6545d.webp"; name="page_04.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/4cfa0e7c2f34c2211cd1f3e7e92f7499.webp"; name="page_05.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/4191c8400b79b56e6fffc4cbe8a1e435.webp"; name="page_06.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/15ac56fc3d01e116b25bb6ef668f7226.webp"; name="page_07.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/5ede99a55ea02e5b9c72c883c77fa5d4.webp"; name="page_08.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/ba6b4282cf09f9b8491a3621055eba30.webp"; name="page_09.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/b33350edefba73230b88b0e0bec09082.webp"; name="page_10.webp"}
)

foreach ($image in $images) {
    $filePath = "ai_coding_report_images\$($image.name)"
    Write-Host "ダウンロード中: $($image.name)"
    try {
        Invoke-WebRequest -Uri $image.url -OutFile $filePath -ErrorAction Stop
        Write-Host "成功: $($image.name)" -ForegroundColor Green
    }
    catch {
        Write-Host "エラー: $($image.name) - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "最初の10枚のダウンロード完了!"
