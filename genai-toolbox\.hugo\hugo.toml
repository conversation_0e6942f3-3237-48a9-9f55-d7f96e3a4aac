title = 'MCP Toolbox for Databases'
relativeURLs = true

languageCode = 'en-us'
defaultContentLanguage = "en"
defaultContentLanguageInSubdir = false

enableGitInfo = true
enableRobotsTXT = true

[languages]
  [languages.en]
    languageName ="English"
    weight = 1

[module]
  proxy = "direct"
  [module.hugoVersion]
    extended = true
    min = "0.146.0"
  [[module.mounts]]
    source = "../docs/en"
    target = 'content'
  [[module.imports]]
    path = "github.com/google/docsy"
    disable = false
  [[module.imports]]
    path = "github.com/martignoni/hugo-notice"

[params]
  description = "MCP Toolbox for Databases is an open source MCP server for databases. It enables you to develop tools easier, faster, and more securely by handling the complexities such as connection pooling, authentication, and more."
  copyright = "Google LLC"
  github_repo = "https://github.com/googleapis/genai-toolbox"
  github_project_repo = "https://github.com/googleapis/genai-toolbox"
  github_subdir = "docs"
  offlineSearch = true
  [params.ui]
    ul_show = 100
    showLightDarkModeMenu = true
    breadcrumb_disable = true
    sidebar_menu_foldable = true
    sidebar_menu_compact = false

[[menu.main]]
  name = "GitHub"
  weight = 50
  url = "https://github.com/googleapis/genai-toolbox"
  pre = "<i class='fa-brands fa-github'></i>"

[markup.goldmark.renderer]
  unsafe= true

[markup.highlight]
  noClasses = false
  style = "tango"

[outputFormats]
  [outputFormats.LLMS]
    mediaType = "text/plain"
    baseName = "llms"
    isPlainText = true
    root = true
  [outputFormats.LLMS-FULL]
    mediaType = "text/plain"
    baseName = "llms-full"
    isPlainText = true
    root = true

[outputs]
  home = ["HTML", "RSS", "LLMS", "LLMS-FULL"]
