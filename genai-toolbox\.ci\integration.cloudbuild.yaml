# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

steps:
  - id: "install-dependencies"
    name: golang:1
    waitFor: ["-"]
    env:
      - "GOPATH=/gopath"
    volumes:
      - name: "go"
        path: "/gopath"
    script: |
      go get -d ./...

  - id: "compile-test-binary"
    name: golang:1
    waitFor: ["install-dependencies"]
    env:
      - "GOPATH=/gopath"
    volumes:
      - name: "go"
        path: "/gopath"
    script: |
      go test -c -race -cover \
      -coverpkg=./internal/sources/...,./internal/tools/... ./tests/...
      chmod +x .ci/test_with_coverage.sh

  - id: "cloud-sql-pg"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "CLOUD_SQL_POSTGRES_PROJECT=$PROJECT_ID"
      - "CLOUD_SQL_POSTGRES_INSTANCE=$_CLOUD_SQL_POSTGRES_INSTANCE"
      - "CLOUD_SQL_POSTGRES_DATABASE=$_DATABASE_NAME"
      - "CLOUD_SQL_POSTGRES_REGION=$_REGION"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv:
      ["CLOUD_SQL_POSTGRES_USER", "CLOUD_SQL_POSTGRES_PASS", "CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Cloud SQL Postgres" \
          cloudsqlpg \
          postgressql \
          postgresexecutesql


  - id: "alloydb-pg"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "ALLOYDB_POSTGRES_PROJECT=$PROJECT_ID"
      - "ALLOYDB_POSTGRES_CLUSTER=$_ALLOYDB_POSTGRES_CLUSTER"
      - "ALLOYDB_POSTGRES_INSTANCE=$_ALLOYDB_POSTGRES_INSTANCE"
      - "ALLOYDB_POSTGRES_DATABASE=$_DATABASE_NAME"
      - "ALLOYDB_POSTGRES_REGION=$_REGION"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv: ["ALLOYDB_POSTGRES_USER", "ALLOYDB_POSTGRES_PASS", "CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "AlloyDB Postgres" \
          alloydbpg \
          postgressql \
          postgresexecutesql

  - id: "alloydb-ai-nl"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "ALLOYDB_AI_NL_PROJECT=$PROJECT_ID"
      - "ALLOYDB_AI_NL_CLUSTER=$_ALLOYDB_AI_NL_CLUSTER"
      - "ALLOYDB_AI_NL_INSTANCE=$_ALLOYDB_AI_NL_INSTANCE"
      - "ALLOYDB_AI_NL_DATABASE=$_DATABASE_NAME"
      - "ALLOYDB_AI_NL_REGION=$_REGION"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv: ["ALLOYDB_AI_NL_USER", "ALLOYDB_AI_NL_PASS", "CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "AlloyDB AI NL" \
          alloydbainl \
          alloydbainl

  - id: "bigtable"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "BIGTABLE_PROJECT=$PROJECT_ID"
      - "BIGTABLE_INSTANCE=$_BIGTABLE_INSTANCE"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv:
      ["CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Bigtable" \
          bigtable \
          bigtable

  - id: "bigquery"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "BIGQUERY_PROJECT=$PROJECT_ID"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv: ["CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "BigQuery" \
          bigquery \
          bigquery
  
  - id: "dataplex"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "DATAPLEX_PROJECT=$PROJECT_ID"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv: ["CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Dataplex" \
          dataplex \
          dataplex

  - id: "postgres"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "POSTGRES_DATABASE=$_DATABASE_NAME"
      - "POSTGRES_HOST=$_POSTGRES_HOST"
      - "POSTGRES_PORT=$_POSTGRES_PORT"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv: ["POSTGRES_USER", "POSTGRES_PASS", "CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Postgres" \
          postgres \
          postgressql \
          postgresexecutesql

  - id: "spanner"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "SPANNER_PROJECT=$PROJECT_ID"
      - "SPANNER_DATABASE=$_DATABASE_NAME"
      - "SPANNER_INSTANCE=$_SPANNER_INSTANCE"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv: ["CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Spanner" \
          spanner \
          spanner

  - id: "neo4j"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "NEO4J_DATABASE=$_NEO4J_DATABASE"
      - "NEO4J_URI=$_NEO4J_URI"
    secretEnv: ["NEO4J_USER", "NEO4J_PASS"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Neo4j" \
          neo4j \
          neo4j

  - id: "cloud-sql-mssql"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "CLOUD_SQL_MSSQL_PROJECT=$PROJECT_ID"
      - "CLOUD_SQL_MSSQL_INSTANCE=$_CLOUD_SQL_MSSQL_INSTANCE"
      - "CLOUD_SQL_MSSQL_IP=$_CLOUD_SQL_MSSQL_IP"
      - "CLOUD_SQL_MSSQL_DATABASE=$_DATABASE_NAME"
      - "CLOUD_SQL_MSSQL_REGION=$_REGION"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv: ["CLOUD_SQL_MSSQL_USER", "CLOUD_SQL_MSSQL_PASS", "CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Cloud SQL MSSQL" \
          cloudsqlmssql \
          mssql

  - id: "cloud-sql-mysql"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "CLOUD_SQL_MYSQL_PROJECT=$PROJECT_ID"
      - "CLOUD_SQL_MYSQL_INSTANCE=$_CLOUD_SQL_MYSQL_INSTANCE"
      - "CLOUD_SQL_MYSQL_DATABASE=$_DATABASE_NAME"
      - "CLOUD_SQL_MYSQL_REGION=$_REGION"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv:
      ["CLOUD_SQL_MYSQL_USER", "CLOUD_SQL_MYSQL_PASS", "CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Cloud SQL MySQL" \
          cloudsqlmysql \
          mysql

  - id: "mysql"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "MYSQL_DATABASE=$_DATABASE_NAME"
      - "MYSQL_HOST=$_MYSQL_HOST"
      - "MYSQL_PORT=$_MYSQL_PORT"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv: ["MYSQL_USER", "MYSQL_PASS", "CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "MySQL" \
          mysql \
          mysql

  - id: "mssql"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "MSSQL_DATABASE=$_DATABASE_NAME"
      - "MSSQL_HOST=$_MSSQL_HOST"
      - "MSSQL_PORT=$_MSSQL_PORT"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv: ["MSSQL_USER", "MSSQL_PASS", "CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "MSSQL" \
          mssql \
          mssql

  - id: "dgraph"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "DGRAPH_URL=$_DGRAPHURL"
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Dgraph" \
          dgraph \
          dgraph

  - id: "http"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
    secretEnv: ["CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "HTTP" \
          http \
          http

  - id: "sqlite"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    volumes:
      - name: "go"
        path: "/gopath"
    secretEnv: ["CLIENT_ID"]
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "SQLite" \
          sqlite \
          sqlite

  - id: "couchbase"
    name : golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "COUCHBASE_SCOPE=$_COUCHBASE_SCOPE"
      - "COUCHBASE_BUCKET=$_COUCHBASE_BUCKET"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv: ["COUCHBASE_CONNECTION", "COUCHBASE_USER", "COUCHBASE_PASS", "CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Couchbase" \
          couchbase \
          couchbase

  - id: "redis"
    name : golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv: ["REDIS_ADDRESS", "REDIS_PASS", "CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Redis" \
          redis \
          redis
        
  - id: "valkey"
    name : golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "VALKEY_DATABASE=$_VALKEY_DATABASE"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv: ["VALKEY_ADDRESS", "CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Valkey" \
          valkey \
          valkey

  - id: "firestore"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "FIRESTORE_PROJECT=$PROJECT_ID"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    secretEnv: ["CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Firestore" \
          firestore \
          firestore

  - id: "looker"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "FIRESTORE_PROJECT=$PROJECT_ID"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
      - "LOOKER_VERIFY_SSL=$_LOOKER_VERIFY_SSL"
    secretEnv: ["CLIENT_ID", "LOOKER_BASE_URL", "LOOKER_CLIENT_ID", "LOOKER_CLIENT_SECRET"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Looker" \
          looker \
          looker
    
  - id: "duckdb"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL"
    volumes:
      - name: "go"
        path: "/gopath"
    secretEnv: ["CLIENT_ID"]
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "DuckDB" \
          duckdb \
          duckdb


  - id: "alloydbwaitforoperation"
    name: golang:1
    waitFor: ["compile-test-binary"]
    entrypoint: /bin/bash
    env:
      - "GOPATH=/gopath"
      - "API_KEY=$(gcloud auth print-access-token)"
    secretEnv: ["CLIENT_ID"]
    volumes:
      - name: "go"
        path: "/gopath"
    args:
      - -c
      - |
        .ci/test_with_coverage.sh \
          "Alloydb Wait for Operation" \
          utility \
          utility/alloydbwaitforoperation
          
availableSecrets:
  secretManager:
    - versionName: projects/$PROJECT_ID/secrets/cloud_sql_pg_user/versions/latest
      env: CLOUD_SQL_POSTGRES_USER
    - versionName: projects/$PROJECT_ID/secrets/cloud_sql_pg_pass/versions/latest
      env: CLOUD_SQL_POSTGRES_PASS
    - versionName: projects/$PROJECT_ID/secrets/alloydb_pg_user/versions/latest
      env: ALLOYDB_POSTGRES_USER
    - versionName: projects/$PROJECT_ID/secrets/alloydb_pg_pass/versions/latest
      env: ALLOYDB_POSTGRES_PASS
    - versionName: projects/$PROJECT_ID/secrets/alloydb_ai_nl_user/versions/latest
      env: ALLOYDB_AI_NL_USER
    - versionName: projects/$PROJECT_ID/secrets/alloydb_ai_nl_pass/versions/latest
      env: ALLOYDB_AI_NL_PASS
    - versionName: projects/$PROJECT_ID/secrets/postgres_user/versions/latest
      env: POSTGRES_USER
    - versionName: projects/$PROJECT_ID/secrets/postgres_pass/versions/latest
      env: POSTGRES_PASS
    - versionName: projects/$PROJECT_ID/secrets/client_id/versions/latest
      env: CLIENT_ID
    - versionName: projects/$PROJECT_ID/secrets/neo4j_user/versions/latest
      env: NEO4J_USER
    - versionName: projects/$PROJECT_ID/secrets/neo4j_pass/versions/latest
      env: NEO4J_PASS
    - versionName: projects/$PROJECT_ID/secrets/cloud_sql_mssql_user/versions/latest
      env: CLOUD_SQL_MSSQL_USER
    - versionName: projects/$PROJECT_ID/secrets/cloud_sql_mssql_pass/versions/latest
      env: CLOUD_SQL_MSSQL_PASS
    - versionName: projects/$PROJECT_ID/secrets/cloud_sql_mysql_user/versions/latest
      env: CLOUD_SQL_MYSQL_USER
    - versionName: projects/$PROJECT_ID/secrets/cloud_sql_mysql_pass/versions/latest
      env: CLOUD_SQL_MYSQL_PASS
    - versionName: projects/$PROJECT_ID/secrets/mysql_user/versions/latest
      env: MYSQL_USER
    - versionName: projects/$PROJECT_ID/secrets/mysql_pass/versions/latest
      env: MYSQL_PASS
    - versionName: projects/$PROJECT_ID/secrets/mssql_user/versions/latest
      env: MSSQL_USER
    - versionName: projects/$PROJECT_ID/secrets/mssql_pass/versions/latest
      env: MSSQL_PASS
    - versionName: projects/$PROJECT_ID/secrets/couchbase_connection/versions/latest
      env: COUCHBASE_CONNECTION
    - versionName: projects/$PROJECT_ID/secrets/couchbase_user/versions/latest
      env: COUCHBASE_USER
    - versionName: projects/$PROJECT_ID/secrets/couchbase_pass/versions/latest
      env: COUCHBASE_PASS
    - versionName: projects/$PROJECT_ID/secrets/memorystore_redis_address/versions/latest
      env: REDIS_ADDRESS
    - versionName: projects/$PROJECT_ID/secrets/memorystore_redis_pass/versions/latest
      env: REDIS_PASS
    - versionName: projects/$PROJECT_ID/secrets/memorystore_valkey_address/versions/latest
      env: VALKEY_ADDRESS
    - versionName: projects/107716898620/secrets/looker_base_url/versions/latest
      env: LOOKER_BASE_URL
    - versionName: projects/107716898620/secrets/looker_client_id/versions/latest
      env: LOOKER_CLIENT_ID
    - versionName: projects/107716898620/secrets/looker_client_secret/versions/latest
      env: LOOKER_CLIENT_SECRET


options:
  logging: CLOUD_LOGGING_ONLY
  automapSubstitutions: true
  substitutionOption: "ALLOW_LOOSE"
  dynamicSubstitutions: true
  pool:
    name: projects/$PROJECT_ID/locations/us-central1/workerPools/integration-testing # Necessary for VPC network connection

substitutions:
  _DATABASE_NAME: test_database
  _REGION: "us-central1"
  _CLOUD_SQL_POSTGRES_INSTANCE: "cloud-sql-pg-testing"
  _ALLOYDB_POSTGRES_CLUSTER: "alloydb-pg-testing"
  _ALLOYDB_POSTGRES_INSTANCE: "alloydb-pg-testing-instance"
  _ALLOYDB_AI_NL_CLUSTER: "alloydb-ai-nl-testing"
  _ALLOYDB_AI_NL_INSTANCE: "alloydb-ai-nl-testing-instance"
  _BIGTABLE_INSTANCE: "bigtable-testing-instance"
  _POSTGRES_HOST: 127.0.0.1
  _POSTGRES_PORT: "5432"
  _SPANNER_INSTANCE: "spanner-testing"
  _NEO4J_DATABASE: "neo4j"
  _CLOUD_SQL_MSSQL_INSTANCE: "cloud-sql-mssql-testing"
  _CLOUD_SQL_MYSQL_INSTANCE: "cloud-sql-mysql-testing"
  _MYSQL_HOST: 127.0.0.1
  _MYSQL_PORT: "3306"
  _MSSQL_HOST: 127.0.0.1
  _MSSQL_PORT: "1433"
  _DGRAPHURL: "https://play.dgraph.io"
  _COUCHBASE_BUCKET: "couchbase-bucket"
  _COUCHBASE_SCOPE: "couchbase-scope"
  _LOOKER_VERIFY_SSL: "true"