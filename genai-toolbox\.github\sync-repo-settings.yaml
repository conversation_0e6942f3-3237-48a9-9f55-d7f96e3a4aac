# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Synchronize repository settings from a centralized config
# https://github.com/googleapis/repo-automation-bots/tree/main/packages/sync-repo-settings
# Install: https://github.com/apps/sync-repo-settings

# Disable merge commits
rebaseMergeAllowed: true
squashMergeAllowed: true
mergeCommitAllowed: false
# Enable branch protection
branchProtectionRules:
  - pattern: main
    isAdminEnforced: true
    requiredStatusCheckContexts:
      - "cla/google"
      - "lint"
      - "conventionalcommits.org"
      - "header-check"
    # - Add required status checks like presubmit tests
      - "unit tests (ubuntu-latest)"
      - "unit tests (windows-latest)"
      - "unit tests (macos-latest)"
      - "integration-test-pr (toolbox-testing-438616)"
    requiredApprovingReviewCount: 1
    requiresCodeOwnerReviews: true
    requiresStrictStatusChecks: true

# Set team access
permissionRules:
  - team: senseai-eco
    permission: admin
