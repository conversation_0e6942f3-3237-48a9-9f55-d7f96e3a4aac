# AI Coding Report Images Download Script - Remaining Images
Write-Host "残りの画像をダウンロードします..."

# 残りの画像URLとファイル名のペア
$images = @(
    @{url="https://developer.qcloudimg.com/http-save/10625241/90dedaa6b1c0db404eeb534dc01b8b38.webp"; name="page_11.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/76a3c73e87e398f3652afc17ae8002f0.webp"; name="page_12.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/dc87b01704b912891fd48724fbe978df.webp"; name="page_13.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/79f79ca09417ebcaf548951fd4c0d39d.webp"; name="page_14.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/320d174ff7a47e7879756b628f86c7de.webp"; name="page_15.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/6da4a64cd49d28cae563963f54c84672.webp"; name="page_16.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/7b2e891ddb1d142f6e61487c4f46c449.webp"; name="page_17.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/9a8439d2ff668e39d34ca175a3f3b725.webp"; name="page_18.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/6a66c80e00575819b5b3a11a892971da.webp"; name="page_19.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/cdb625e37b24ef3bf38421a47ae368de.webp"; name="page_20.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/f7e7b18b6f959ad315ffdc1ff05a4af0.webp"; name="page_21.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/804616ab40d6c93377f3ef62a22a5aac.webp"; name="page_22.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/b9d95391ad6627de4a570f71e37c4502.webp"; name="page_23.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/af50b5ae9f2d704b0c156e8e219338ba.webp"; name="page_24.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/96f9eb569e53e2788184eeca04ba764b.webp"; name="page_25.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/0677838ffccf07384054d086ea9b75d7.webp"; name="page_26.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/97fd660ba70a02b4fe230fd36e5f8ed7.webp"; name="page_27.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/96fb647098cada5a59c28ef7fac60b43.webp"; name="page_28.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/8eaffc860c1ca2675ce0154e7d0df536.webp"; name="page_29.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/ad4248839791ffbbccd11d88551c26b2.webp"; name="page_30.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/362e37c494c0cff83dd7f11028bd431b.webp"; name="page_31.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/a47c4461c3f2c8faf9f85bc63aa63d2b.webp"; name="page_32.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/e4c07486517a9ec813fa35d5775011e5.webp"; name="page_33.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/ef1d5ae5a9540c0ce196e105b2a2bab4.webp"; name="page_34.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/065d481f0e518d9c0ee992e9f7584ba3.webp"; name="page_35.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/459872911e6ac065329d22e2ae1a24eb.webp"; name="page_36.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/1cc8b62e896a369288063ee9383f8396.webp"; name="page_37.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/7771c7f3312cd04e985a1fdf2228f4a0.webp"; name="page_38.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/1421b6b02fb2781b48c8b45510b41f41.webp"; name="page_39.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/027b59bb990233243cbdba13260e07b5.webp"; name="page_40.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/49b8c80cd8a4c5f9adce5b183fe61a17.webp"; name="page_41.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/79737defad7b82cb27f409f38faf4a4d.webp"; name="page_42.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/b65b094a39c1b7ba143cb1f4c77e511e.webp"; name="page_43.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/dd2bf1a0e8331ee9f4989e7171b7bb86.webp"; name="page_44.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/572e51b5214078455ff531f089e2967b.webp"; name="page_45.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/1d4cee0499a1c693e9f7e0dcda0cf54b.webp"; name="page_46.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/d150621ac4ad2658148abe643e01801b.webp"; name="page_47.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/6253e79e6f6558029474eee68b5d7c2b.webp"; name="page_48.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/ffd664bdba1b4ffffa1efe13f22e81bb.webp"; name="page_49.webp"},
    @{url="https://developer.qcloudimg.com/http-save/10625241/7cbb3a86f4e948034e851c4742a57b6b.webp"; name="page_50.webp"}
)

foreach ($image in $images) {
    $filePath = "ai_coding_report_images\$($image.name)"
    Write-Host "ダウンロード中: $($image.name)"
    try {
        Invoke-WebRequest -Uri $image.url -OutFile $filePath -ErrorAction Stop
        Write-Host "成功: $($image.name)" -ForegroundColor Green
    }
    catch {
        Write-Host "エラー: $($image.name) - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "残りの画像のダウンロード完了!"
