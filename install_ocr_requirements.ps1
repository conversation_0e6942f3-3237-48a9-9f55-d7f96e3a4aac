# OCR库安装脚本
Write-Host "开始安装OCR相关库..." -ForegroundColor Green

# 检查Python是否安装
try {
    $pythonVersion = python --version
    Write-Host "Python版本: $pythonVersion" -ForegroundColor Green
}
catch {
    Write-Host "错误: 未找到Python，请先安装Python" -ForegroundColor Red
    exit 1
}

# 升级pip
Write-Host "升级pip..." -ForegroundColor Yellow
python -m pip install --upgrade pip

# 安装基础图像处理库
Write-Host "安装基础库..." -ForegroundColor Yellow
pip install Pillow
pip install opencv-python
pip install numpy

# 选择安装OCR库
Write-Host "请选择要安装的OCR库:" -ForegroundColor Cyan
Write-Host "1. Tesseract + pytesseract (需要额外安装Tesseract程序)"
Write-Host "2. EasyOCR (推荐，简单易用)"
Write-Host "3. PaddleOCR (中文效果好)"
Write-Host "4. 全部安装"

$choice = Read-Host "请输入选择 (1-4)"

switch ($choice) {
    "1" {
        Write-Host "安装pytesseract..." -ForegroundColor Yellow
        pip install pytesseract
        
        Write-Host "注意: 还需要安装Tesseract程序" -ForegroundColor Red
        Write-Host "请访问: https://github.com/UB-Mannheim/tesseract/wiki" -ForegroundColor Blue
        Write-Host "或运行: winget install UB-Mannheim.TesseractOCR" -ForegroundColor Blue
    }
    "2" {
        Write-Host "安装EasyOCR..." -ForegroundColor Yellow
        pip install easyocr
        pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
    }
    "3" {
        Write-Host "安装PaddleOCR..." -ForegroundColor Yellow
        pip install paddlepaddle
        pip install paddleocr
    }
    "4" {
        Write-Host "安装所有OCR库..." -ForegroundColor Yellow
        
        # Tesseract
        pip install pytesseract
        
        # EasyOCR
        pip install easyocr
        pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
        
        # PaddleOCR
        pip install paddlepaddle
        pip install paddleocr
        
        Write-Host "注意: Tesseract还需要安装程序" -ForegroundColor Red
        Write-Host "请运行: winget install UB-Mannheim.TesseractOCR" -ForegroundColor Blue
    }
    default {
        Write-Host "无效选择，退出安装" -ForegroundColor Red
        exit 1
    }
}

Write-Host "安装完成!" -ForegroundColor Green
Write-Host "现在可以运行 python ocr_example.py 来提取图片文字" -ForegroundColor Cyan
