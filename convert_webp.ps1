# WebP to PNG Converter
Add-Type -AssemblyName System.Drawing

try {
    $webpPath = "ai_coding_report_images\page_01.webp"
    $pngPath = "page_01.png"
    
    Write-Host "Converting $webpPath to $pngPath"
    
    $img = [System.Drawing.Image]::FromFile($webpPath)
    $img.Save($pngPath, [System.Drawing.Imaging.ImageFormat]::Png)
    $img.Dispose()
    
    Write-Host "Conversion completed successfully!"
}
catch {
    Write-Host "Error: $($_.Exception.Message)"
}
